{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "supabase": {"command": "npx", "args": ["-y", "@supabase-community/supabase-mcp"], "env": {"SUPABASE_URL": "https://hswatweayaatytzzyrdg.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhzd2F0d2VheWFhdHl0enp5cmRnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUyMjIwNDgsImV4cCI6MjA2MDc5ODA0OH0.B8DLy22f0KRQsJlout0QEoAAlOV9xvZTVXstXW1ueC8"}, "alwaysAllow": ["query"]}}}