'use client'

import { useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'

export default function AuthWrapper({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Solo verificar autenticación en rutas admin
    if (pathname.startsWith('/admin')) {
      const token = localStorage.getItem('sb-access-token')
      
      if (!token) {
        console.log('AuthWrapper - No token found, redirecting to login')
        router.push(`/login?redirectTo=${pathname}`)
        return
      }

      console.log('AuthWrapper - Token found, user is authenticated')
      
      // Interceptar todas las requests para agregar el token
      const originalFetch = window.fetch
      window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
        const token = localStorage.getItem('sb-access-token')
        if (token) {
          init = init || {}
          init.headers = {
            ...init.headers,
            'Authorization': `Bearer ${token}`
          }
        }
        return originalFetch(input, init)
      }
    }
  }, [pathname, router])

  return <>{children}</>
}
