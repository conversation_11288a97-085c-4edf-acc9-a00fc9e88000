"use client"

import { useActionState, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, Send, Mail, Phone, User, Building, MessageSquare, DollarSign, Calendar, Zap } from "lucide-react"
import { submitContactForm, type ContactFormState } from "@/lib/contact-actions"

interface SimpleContactFormProps {
  service: string
  title?: string
  description?: string
  showBudget?: boolean
  showTimeline?: boolean
}

const initialState: ContactFormState = {}

// Formatear teléfono para España
const formatPhone = (value: string) => {
  const digits = value.replace(/\D/g, '')
  if (digits.length === 0) return ''
  if (digits.length <= 3) return digits
  if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`
  if (digits.length <= 9) return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`
  return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`
}

export default function SimpleContactForm({
  service,
  title = "¿Listo para comenzar?",
  description = "Completa el formulario y te contactaremos en menos de 24 horas",
  showBudget = false,
  showTimeline = false,
}: SimpleContactFormProps) {
  const [state, formAction, isPending] = useActionState(submitContactForm, initialState)
  const [phone, setPhone] = useState('')

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value)
    setPhone(formatted)
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader className="text-center space-y-4">
          <Badge variant="outline" className="mx-auto">
            {service}
          </Badge>
          
          <CardTitle className="text-2xl font-bold text-white">
            {title}
          </CardTitle>
          
          <CardDescription className="text-slate-300">
            {description}
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form action={formAction} className="space-y-6">
            {/* Mostrar mensaje de éxito */}
            {state.success && (
              <div className="bg-green-900/50 border border-green-500/50 rounded-2xl p-6 text-center">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">
                  ¡Perfecto!
                </h3>
                <p className="text-green-200 whitespace-pre-line">
                  {state.message}
                </p>
              </div>
            )}

            {/* Solo mostrar el formulario si no se ha enviado exitosamente */}
            {!state.success && (
              <>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Nombre */}
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-white font-medium flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Nombre completo *
                    </Label>
                    <Input 
                      id="name" 
                      name="name" 
                      type="text" 
                      required 
                      placeholder="Tu nombre completo"
                      className="bg-slate-800/50 border-slate-600"
                    />
                    {state.errors?.name && (
                      <p className="text-red-400 text-sm flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {state.errors.name[0]}
                      </p>
                    )}
                  </div>

                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white font-medium flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email *
                    </Label>
                    <Input 
                      id="email" 
                      name="email" 
                      type="email" 
                      required 
                      placeholder="<EMAIL>"
                      className="bg-slate-800/50 border-slate-600"
                    />
                    {state.errors?.email && (
                      <p className="text-red-400 text-sm flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {state.errors.email[0]}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  {/* Teléfono */}
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-white font-medium flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Teléfono
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="123 456 789"
                      value={phone}
                      onChange={handlePhoneChange}
                      className="bg-slate-800/50 border-slate-600"
                    />
                    {state.errors?.phone && (
                      <p className="text-red-400 text-sm flex items-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        {state.errors.phone[0]}
                      </p>
                    )}
                  </div>

                  {/* Empresa */}
                  <div className="space-y-2">
                    <Label htmlFor="company" className="text-white font-medium flex items-center gap-2">
                      <Building className="w-4 h-4" />
                      Empresa
                    </Label>
                    <Input 
                      id="company" 
                      name="company" 
                      type="text" 
                      placeholder="Nombre de tu empresa"
                      className="bg-slate-800/50 border-slate-600"
                    />
                  </div>
                </div>

                {/* Servicio (hidden field) */}
                <input type="hidden" name="service" value={service} />

                {/* Presupuesto y Timeline */}
                {(showBudget || showTimeline) && (
                  <div className="grid md:grid-cols-2 gap-6">
                    {showBudget && (
                      <div className="space-y-2">
                        <Label className="text-white font-medium flex items-center gap-2">
                          <DollarSign className="w-4 h-4" />
                          Presupuesto estimado
                        </Label>
                        <Select name="budget">
                          <SelectTrigger className="bg-slate-800/50 border-slate-600">
                            <SelectValue placeholder="Selecciona un rango" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1000-5000">€1,000 - €5,000</SelectItem>
                            <SelectItem value="5000-10000">€5,000 - €10,000</SelectItem>
                            <SelectItem value="10000-25000">€10,000 - €25,000</SelectItem>
                            <SelectItem value="25000+">€25,000+</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {showTimeline && (
                      <div className="space-y-2">
                        <Label className="text-white font-medium flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Timeline del proyecto
                        </Label>
                        <Select name="timeline">
                          <SelectTrigger className="bg-slate-800/50 border-slate-600">
                            <SelectValue placeholder="¿Cuándo necesitas comenzar?" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="inmediato">Inmediatamente</SelectItem>
                            <SelectItem value="1-2-semanas">1-2 semanas</SelectItem>
                            <SelectItem value="1-mes">En 1 mes</SelectItem>
                            <SelectItem value="2-3-meses">2-3 meses</SelectItem>
                            <SelectItem value="flexible">Flexible</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>
                )}

                {/* Mensaje */}
                <div className="space-y-2">
                  <Label htmlFor="message" className="text-white font-medium flex items-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    Cuéntanos sobre tu proyecto *
                  </Label>
                  <Textarea
                    id="message"
                    name="message"
                    required
                    rows={4}
                    placeholder="Describe tu proyecto, objetivos y cualquier detalle importante..."
                    className="bg-slate-800/50 border-slate-600 resize-none"
                  />
                  {state.errors?.message && (
                    <p className="text-red-400 text-sm flex items-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      {state.errors.message[0]}
                    </p>
                  )}
                </div>

                {/* Botón de envío */}
                <Button 
                  type="submit" 
                  disabled={isPending} 
                  size="lg" 
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  {isPending ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Enviando consulta...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Send className="w-5 h-5" />
                      <span>Enviar Consulta</span>
                    </div>
                  )}
                </Button>

                <p className="text-slate-400 text-sm text-center font-light">
                  * Campos obligatorios. Te contactaremos en menos de 24 horas.
                </p>
              </>
            )}
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
