"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  AlertTriangle,
  CheckCircle2,
  Target,
  Calendar
} from "lucide-react"
import { motion } from "framer-motion"
import type { ContactStats } from "@/lib/database"

interface DashboardChartsProps {
  stats: ContactStats & {
    needing_followup?: number
    followup_contacts?: any[]
  }
}

export default function DashboardCharts({ stats }: DashboardChartsProps) {
  // Calcular porcentajes para los gráficos
  const statusPercentages = Object.entries(stats.by_status).map(([status, count]) => ({
    status,
    count,
    percentage: (count / stats.total) * 100
  }))

  const servicePercentages = Object.entries(stats.by_service).map(([service, count]) => ({
    service,
    count,
    percentage: (count / stats.total) * 100
  }))

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-500"
      case "contacted": return "bg-yellow-500"
      case "in_progress": return "bg-purple-500"
      case "quoted": return "bg-orange-500"
      case "closed": return "bg-green-500"
      case "lost": return "bg-red-500"
      default: return "bg-gray-500"
    }
  }

  const getServiceColor = (index: number) => {
    const colors = ["bg-teal-500", "bg-blue-500", "bg-purple-500", "bg-orange-500"]
    return colors[index % colors.length]
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Distribución por Estado */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
      >
        <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
          <CardHeader className="p-6">
            <CardTitle className="text-white flex items-center gap-2">
              <BarChart3 className="w-5 h-5 text-teal-400" />
              Distribución por Estado
            </CardTitle>
            <CardDescription className="text-slate-400">
              Estado actual de todas las consultas
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="space-y-4">
              {statusPercentages.map(({ status, count, percentage }) => (
                <div key={status} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-white capitalize">
                      {status.replace('_', ' ')}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-slate-400">{count}</span>
                      <Badge variant="outline" className="text-xs">
                        {percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                  <div className="relative">
                    <Progress 
                      value={percentage} 
                      className="h-2 bg-slate-800"
                    />
                    <div 
                      className={`absolute top-0 left-0 h-2 rounded-full ${getStatusColor(status)}`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Distribución por Servicio */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
          <CardHeader className="p-6">
            <CardTitle className="text-white flex items-center gap-2">
              <Target className="w-5 h-5 text-purple-400" />
              Servicios Más Solicitados
            </CardTitle>
            <CardDescription className="text-slate-400">
              Distribución de consultas por tipo de servicio
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="space-y-4">
              {servicePercentages.map(({ service, count, percentage }, index) => (
                <div key={service} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-white">
                      {service}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-slate-400">{count}</span>
                      <Badge variant="outline" className="text-xs">
                        {percentage.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                  <div className="relative">
                    <Progress 
                      value={percentage} 
                      className="h-2 bg-slate-800"
                    />
                    <div 
                      className={`absolute top-0 left-0 h-2 rounded-full ${getServiceColor(index)}`}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Métricas de Rendimiento */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
          <CardHeader className="p-6">
            <CardTitle className="text-white flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              Métricas de Rendimiento
            </CardTitle>
            <CardDescription className="text-slate-400">
              Indicadores clave de rendimiento
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-600/20 rounded-xl flex items-center justify-center">
                    <CheckCircle2 className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-white">Tasa de Respuesta</p>
                    <p className="text-xs text-slate-400">Consultas respondidas</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-green-400">{stats.response_rate}%</p>
                  <p className="text-xs text-slate-400">Excelente</p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-600/20 rounded-xl flex items-center justify-center">
                    <Clock className="w-5 h-5 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-white">Tiempo Promedio</p>
                    <p className="text-xs text-slate-400">Respuesta inicial</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-400">{stats.avg_response_time}h</p>
                  <p className="text-xs text-slate-400">Rápido</p>
                </div>
              </div>

              {stats.needing_followup !== undefined && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-orange-600/20 rounded-xl flex items-center justify-center">
                      <AlertTriangle className="w-5 h-5 text-orange-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">Requieren Seguimiento</p>
                      <p className="text-xs text-slate-400">Consultas pendientes</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-orange-400">{stats.needing_followup}</p>
                    <p className="text-xs text-slate-400">Atención</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Actividad Reciente */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
          <CardHeader className="p-6">
            <CardTitle className="text-white flex items-center gap-2">
              <Calendar className="w-5 h-5 text-purple-400" />
              Actividad del Período
            </CardTitle>
            <CardDescription className="text-slate-400">
              Resumen de consultas por período
            </CardDescription>
          </CardHeader>
          <CardContent className="p-6 pt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-slate-800/30 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-teal-600/20 rounded-lg flex items-center justify-center">
                    <Users className="w-4 h-4 text-teal-400" />
                  </div>
                  <span className="text-sm font-medium text-white">Esta Semana</span>
                </div>
                <Badge variant="outline" className="text-teal-400 border-teal-400/30">
                  {stats.this_week} consultas
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 bg-slate-800/30 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-600/20 rounded-lg flex items-center justify-center">
                    <Calendar className="w-4 h-4 text-blue-400" />
                  </div>
                  <span className="text-sm font-medium text-white">Este Mes</span>
                </div>
                <Badge variant="outline" className="text-blue-400 border-blue-400/30">
                  {stats.this_month} consultas
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 bg-slate-800/30 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-600/20 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-4 h-4 text-purple-400" />
                  </div>
                  <span className="text-sm font-medium text-white">Total Histórico</span>
                </div>
                <Badge variant="outline" className="text-purple-400 border-purple-400/30">
                  {stats.total} consultas
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
