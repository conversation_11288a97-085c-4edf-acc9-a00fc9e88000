"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { createClientSupabaseClient } from '@/lib/supabase-client'
import AdminSidebar from './admin-sidebar'
import AdminHeader from './admin-header'
import type { User } from '@supabase/supabase-js'

interface AdminLayoutClientProps {
  children: React.ReactNode
}

export default function AdminLayoutClient({ children }: AdminLayoutClientProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const router = useRouter()

  useEffect(() => {
    // Verificar autenticación usando endpoint seguro con refresh automático
    const checkAuth = async () => {
      try {
        let response = await fetch('/api/auth/verify', {
          method: 'GET',
          credentials: 'include', // Incluir cookies
        })

        let data = await response.json()

        // Si el token expiró, intentar renovarlo
        if (!response.ok && response.status === 401) {
          console.log('Access token expired, attempting refresh...')

          const refreshResponse = await fetch('/api/auth/refresh', {
            method: 'POST',
            credentials: 'include',
          })

          if (refreshResponse.ok) {
            console.log('Token refreshed successfully, retrying verification...')
            // Reintentar verificación con el nuevo token
            response = await fetch('/api/auth/verify', {
              method: 'GET',
              credentials: 'include',
            })
            data = await response.json()
          }
        }

        if (!response.ok || !data.authenticated) {
          console.log('Authentication failed, redirecting to login')
          router.push('/login')
          return
        }

        console.log('User authenticated successfully')
        // Crear usuario con datos del servidor
        setUser({
          id: data.user.id,
          email: data.user.email,
          aud: 'authenticated',
          role: 'authenticated',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          app_metadata: { role: data.user.role },
          user_metadata: {},
        } as User)

      } catch (error) {
        console.error('Error checking auth:', error)
        router.push('/login')
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [router])

  if (loading) {
    return (
      <div className="min-h-screen bg-hero-gradient flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="w-12 h-12 border-4 border-brand-primary border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <p className="text-slate-300 text-lg font-medium">Cargando panel de administración...</p>
        </motion.div>
      </div>
    )
  }

  if (!user) {
    return null // El useEffect redirigirá a login
  }

  return (
    <div className="min-h-screen bg-hero-gradient overflow-x-hidden">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-primary/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-brand-accent/5 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      </div>

      <div className="flex relative z-10">
        {/* Sidebar */}
        <AdminSidebar
          user={user}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />

        {/* Main Content */}
        <div className="flex-1 lg:ml-64">
          <AdminHeader
            user={user}
            onMenuToggle={() => setSidebarOpen(!sidebarOpen)}
          />
          <motion.main
            className="p-6 lg:p-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            {children}
          </motion.main>
        </div>
      </div>
    </div>
  )
}
