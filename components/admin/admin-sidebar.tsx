"use client"

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Users,
  Mail,
  Settings,
  BarChart3,
  FileText,
  Shield,
  ChevronLeft,
  ChevronRight,
  LogOut
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { supabaseClient } from '@/lib/supabase-client'
import { useRouter } from 'next/navigation'
import type { User } from '@supabase/supabase-js'

interface AdminSidebarProps {
  user: User
  isOpen?: boolean
  onClose?: () => void
}

const navigation = [
  { name: 'Dashboard', href: '/admin/dashboard', icon: LayoutDashboard },
  { name: 'Contactos', href: '/admin/contacts', icon: Users },
  { name: 'Emails', href: '/admin/emails', icon: Mail },
  { name: 'Reportes', href: '/admin/reports', icon: BarChart3 },
  { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/admin/users', icon: Shield },
  { name: 'Configuración', href: '/admin/settings', icon: Settings },
]

export default function AdminSidebar({ user, isOpen = false, onClose }: AdminSidebarProps) {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()
  const router = useRouter()

  const handleLogout = async () => {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      })

      if (response.ok) {
        router.push('/login')
        router.refresh()
      }
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 bg-slate-900/95 backdrop-blur-xl border-r border-slate-700/50 transition-all duration-300 ${
        collapsed ? 'w-16' : 'w-64'
      } ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0`}>

        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700/50">
          {!collapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-brand-primary to-brand-accent rounded-lg flex items-center justify-center shadow-lg">
                <Shield className="w-4 h-4 text-white" />
              </div>
              <div>
                <h2 className="text-white font-bold text-lg">Karedesk</h2>
                <p className="text-brand-primary text-xs font-medium">Admin Panel</p>
              </div>
            </div>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="text-slate-400 hover:text-white hover:bg-slate-800/50 rounded-xl transition-all duration-200"
          >
            {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
          </Button>
        </div>

        {/* User Info */}
        {!collapsed && (
          <div className="p-4 border-b border-slate-700/50">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-brand-primary to-brand-accent rounded-full flex items-center justify-center shadow-lg hover:scale-105 transition-transform duration-200">
                <span className="text-white font-bold text-sm">
                  {user.email?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-white text-sm font-medium truncate">
                  {user.email}
                </p>
                <p className="text-brand-primary text-xs font-medium">
                  Administrador
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center space-x-3 px-3 py-3 rounded-xl transition-all duration-300 hover:translate-x-1 ${
                      isActive
                        ? 'bg-gradient-to-r from-brand-primary to-brand-accent text-white shadow-lg'
                        : 'text-slate-300 hover:text-white hover:bg-slate-800/50'
                    }`}
                  >
                    <item.icon className="w-5 h-5 flex-shrink-0" />
                    {!collapsed && (
                      <span className="font-medium">{item.name}</span>
                    )}
                  </Link>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* Logout */}
        <div className="p-4 border-t border-slate-700/50">
          <Button
            onClick={handleLogout}
            variant="ghost"
            className={`w-full text-slate-300 hover:text-white hover:bg-red-600/20 hover:border-red-500/30 border border-transparent rounded-xl transition-all duration-300 ${
              collapsed ? 'px-2' : 'justify-start'
            }`}
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
            {!collapsed && (
              <span className="ml-3 font-medium">Cerrar Sesión</span>
            )}
          </Button>
        </div>
      </div>
    </>
  )
}
