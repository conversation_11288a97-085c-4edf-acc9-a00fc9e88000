"use client"

import { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Users,
  Mail,
  TrendingUp,
  Clock,
  ArrowUpRight,
  Calendar,
  MessageSquare,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'

interface Contact {
  id: number
  name: string
  email: string
  phone?: string
  company?: string
  service: string
  message: string
  created_at: string
}

interface Stats {
  total: number
  thisMonth: number
  thisWeek: number
  byService: Record<string, number>
}

export default function DashboardClient() {
  const [stats, setStats] = useState<Stats>({
    total: 0,
    thisMonth: 0,
    thisWeek: 0,
    byService: {}
  })
  const [recentContacts, setRecentContacts] = useState<Contact[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  const fetchData = useCallback(async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true)
    } else {
      setLoading(true)
    }

    try {
      // Obtener estadísticas básicas desde la API
      const [statsResponse, contactsResponse] = await Promise.all([
        fetch('/api/admin/stats', { cache: 'no-store' }),
        fetch('/api/admin/contacts?limit=5', { cache: 'no-store' })
      ])

      if (statsResponse.ok) {
        const response = await statsResponse.json()
        if (response.success) {
          setStats(response.data)
        }
      }

      if (contactsResponse.ok) {
        const contactsData = await contactsResponse.json()
        if (contactsData.success && contactsData.data) {
          setRecentContacts(contactsData.data)
        } else {
          setRecentContacts([])
        }
      }

      setLastUpdated(new Date())
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])

  useEffect(() => {
    // Cargar datos iniciales
    fetchData()

    // Configurar actualización automática cada 30 segundos
    const interval = setInterval(() => {
      fetchData(true)
    }, 30000)

    return () => clearInterval(interval)
  }, [fetchData])

  // Calcular estadísticas dinámicas basadas en datos reales
  const totalServices = Object.values(stats.byService).reduce((sum, count) => sum + count, 0)
  const responseRate = totalServices > 0 ? Math.round((stats.total / totalServices) * 100) : 0

  const statCards = [
    {
      title: 'Total Contactos',
      value: stats.total,
      icon: Users,
      description: 'Desde el inicio'
    },
    {
      title: 'Este Mes',
      value: stats.thisMonth,
      icon: Calendar,
      description: 'Contactos nuevos'
    },
    {
      title: 'Esta Semana',
      value: stats.thisWeek,
      icon: Clock,
      description: 'Últimos 7 días'
    },
    {
      title: 'Servicios Activos',
      value: Object.keys(stats.byService).length,
      icon: TrendingUp,
      description: 'Tipos de servicios'
    }
  ]

  if (loading) {
    return (
      <div className="space-y-6">
        <motion.div
          className="animate-pulse"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          <div className="h-8 bg-slate-700/50 rounded w-1/4 mb-2"></div>
          <div className="h-4 bg-slate-700/50 rounded w-1/2"></div>
        </motion.div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="animate-pulse"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.1 }}
            >
              <div className="h-32 bg-slate-800/50 rounded-3xl border border-slate-700/50"></div>
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <motion.div
        className="flex items-center justify-between"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div>
          <motion.h1
            className="text-4xl md:text-5xl font-bold text-white mb-2 tracking-tight"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            Dashboard
          </motion.h1>
          <div className="flex items-center space-x-4">
            <motion.p
              className="text-xl text-slate-300 font-light"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              Resumen de actividad de Karedesk España
            </motion.p>
            <AnimatePresence>
              {lastUpdated && (
                <motion.p
                  className="text-sm text-brand-primary font-medium"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                >
                  Última actualización: {lastUpdated.toLocaleTimeString('es-ES')}
                </motion.p>
              )}
            </AnimatePresence>
          </div>
        </div>
        <motion.div
          className="flex items-center space-x-3"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.4 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchData(true)}
              disabled={refreshing}
              className="border-slate-600 text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-xl"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              {refreshing ? 'Actualizando...' : 'Actualizar'}
            </Button>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button variant="gradient" asChild className="shadow-brand">
              <Link href="/admin/contacts">
                Ver Todos los Contactos
              </Link>
            </Button>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className={`bg-slate-800/50 border-slate-700/50 backdrop-blur-sm transition-opacity ${refreshing ? 'opacity-70' : 'opacity-100'}`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-400">
                {stat.title}
              </CardTitle>
              <stat.icon className="h-4 w-4 text-slate-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">
                {refreshing ? (
                  <div className="animate-pulse bg-slate-600 h-8 w-16 rounded"></div>
                ) : (
                  stat.value
                )}
              </div>
              <p className="text-xs text-slate-400 mt-1">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Contacts */}
        <Card className={`bg-slate-800/50 border-slate-700/50 backdrop-blur-sm transition-opacity ${refreshing ? 'opacity-70' : 'opacity-100'}`}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-white">
              <MessageSquare className="w-5 h-5" />
              <span>Contactos Recientes</span>
            </CardTitle>
            <CardDescription className="text-slate-400">
              Últimas consultas recibidas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {refreshing ? (
                // Skeleton loading para contactos
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-slate-600 rounded animate-pulse"></div>
                      <div className="h-3 bg-slate-600 rounded w-3/4 animate-pulse"></div>
                      <div className="h-3 bg-slate-600 rounded w-1/2 animate-pulse"></div>
                    </div>
                    <div className="w-12 h-8 bg-slate-600 rounded animate-pulse"></div>
                  </div>
                ))
              ) : recentContacts.length > 0 ? (
                recentContacts.map((contact) => (
                  <div key={contact.id} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-white">
                          {contact.name}
                        </p>
                        <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                          {contact.service}
                        </Badge>
                      </div>
                      <p className="text-sm text-slate-400">
                        {contact.email}
                      </p>
                      <p className="text-xs text-slate-500">
                        {new Date(contact.created_at).toLocaleDateString('es-ES', {
                          day: 'numeric',
                          month: 'short',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                    <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white hover:bg-slate-600/50" asChild>
                      <Link href={`/admin/contacts/${contact.id}`}>
                        Ver
                      </Link>
                    </Button>
                  </div>
                ))
              ) : (
                <p className="text-slate-400 text-center py-4">
                  No hay contactos recientes
                </p>
              )}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50" asChild>
                <Link href="/admin/contacts">
                  Ver Todos los Contactos
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Services Breakdown */}
        <Card className={`bg-slate-800/50 border-slate-700/50 backdrop-blur-sm transition-opacity ${refreshing ? 'opacity-70' : 'opacity-100'}`}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-white">
              <TrendingUp className="w-5 h-5" />
              <span>Servicios Más Solicitados</span>
            </CardTitle>
            <CardDescription className="text-slate-400">
              Distribución por tipo de servicio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.keys(stats.byService).length > 0 ? (
                Object.entries(stats.byService)
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 5)
                  .map(([service, count]) => {
                    const maxCount = Math.max(...Object.values(stats.byService))
                    const percentage = maxCount > 0 ? (count / maxCount) * 100 : 0

                    return (
                      <div key={service} className="flex items-center justify-between">
                        <div className="flex-1">
                          <p className="font-medium text-white">
                            {service}
                          </p>
                          <div className="w-full bg-slate-600 rounded-full h-2 mt-1">
                            <div
                              className="bg-brand-primary h-2 rounded-full transition-all duration-300"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                        <Badge variant="secondary" className="ml-3 bg-slate-700 text-slate-300">
                          {count}
                        </Badge>
                      </div>
                    )
                  })
              ) : (
                <p className="text-slate-400 text-center py-4">
                  No hay datos de servicios disponibles
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white">Acciones Rápidas</CardTitle>
          <CardDescription className="text-slate-400">
            Tareas comunes de administración
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-20 flex-col space-y-2 border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50" asChild>
              <Link href="/admin/contacts">
                <Users className="w-6 h-6" />
                <span>Gestionar Contactos</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2 border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50" asChild>
              <Link href="/admin/emails">
                <Mail className="w-6 h-6" />
                <span>Enviar Emails</span>
              </Link>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2 border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700/50" asChild>
              <Link href="/admin/reports">
                <TrendingUp className="w-6 h-6" />
                <span>Ver Reportes</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
