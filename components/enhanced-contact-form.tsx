"use client"

import { useActionState, useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { FormField } from "@/components/ui/form-field"
import { Notification } from "@/components/ui/notification"
import { 
  Send, 
  User, 
  Mail, 
  Phone, 
  Building, 
  MessageSquare, 
  DollarSign, 
  Calendar,
  Zap,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { submitContactForm, type ContactFormState } from "@/lib/contact-actions"
import { useContactForm } from "@/hooks/use-contact-form"

interface EnhancedContactFormProps {
  service: string
  title?: string
  description?: string
  showBudget?: boolean
  showTimeline?: boolean
}

const initialState: ContactFormState = {}

export default function EnhancedContactForm({
  service,
  title = "¿Listo para comenzar?",
  description = "Completa el formulario y te contactaremos en menos de 24 horas",
  showBudget = false,
  showTimeline = false,
}: EnhancedContactFormProps) {
  const [state, formAction, isPending] = useActionState(submitContactForm, initialState)
  const [showNotification, setShowNotification] = useState(false)
  
  const {
    formData,
    fieldErrors,
    touched,
    handleFieldChange,
    handleFieldBlur,
    isFormValid,
    resetForm
  } = useContactForm()

  // Mostrar notificación cuando hay un resultado
  useEffect(() => {
    if (state.success || state.message) {
      setShowNotification(true)
      if (state.success) {
        resetForm()
      }
    }
  }, [state, resetForm])

  return (
    <>
      {/* Notification */}
      <Notification
        show={showNotification}
        type={state.success ? 'success' : 'error'}
        title={state.success ? '¡Consulta enviada!' : 'Error en el envío'}
        message={state.message || 'Ha ocurrido un error inesperado'}
        onClose={() => setShowNotification(false)}
      />

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl mx-auto"
      >
        <Card className="bg-slate-900/50 border-slate-700/50 backdrop-blur-sm">
          <CardHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring" }}
            >
              <Badge variant="outline" className="mx-auto">
                {service}
              </Badge>
            </motion.div>
            
            <CardTitle className="text-2xl font-bold text-white">
              {title}
            </CardTitle>
            
            <CardDescription className="text-slate-300">
              {description}
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form action={formAction} className="space-y-6">
              {/* Mostrar mensaje de éxito */}
              <AnimatePresence>
                {state.success && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="bg-green-900/50 border border-green-500/50 rounded-2xl p-6 text-center"
                  >
                    <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">
                      ¡Perfecto!
                    </h3>
                    <p className="text-green-200 whitespace-pre-line">
                      {state.message}
                    </p>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Solo mostrar el formulario si no se ha enviado exitosamente */}
              {!state.success && (
                <>
                  <div className="grid md:grid-cols-2 gap-6">
                    <FormField
                      id="name"
                      name="name"
                      label="Nombre completo"
                      type="text"
                      placeholder="Tu nombre completo"
                      required
                      value={formData.name}
                      error={fieldErrors.name || state.errors?.name?.[0]}
                      touched={touched.name}
                      icon={<User className="w-4 h-4" />}
                      delay={0.1}
                      onChange={(value) => handleFieldChange('name', value)}
                      onBlur={() => handleFieldBlur('name')}
                    />

                    <FormField
                      id="email"
                      name="email"
                      label="Email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      value={formData.email}
                      error={fieldErrors.email || state.errors?.email?.[0]}
                      touched={touched.email}
                      icon={<Mail className="w-4 h-4" />}
                      delay={0.2}
                      onChange={(value) => handleFieldChange('email', value)}
                      onBlur={() => handleFieldBlur('email')}
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <FormField
                      id="phone"
                      name="phone"
                      label="Teléfono"
                      type="tel"
                      placeholder="123 456 789"
                      required={false}
                      value={formData.phone}
                      error={fieldErrors.phone || state.errors?.phone?.[0]}
                      touched={touched.phone}
                      icon={<Phone className="w-4 h-4" />}
                      delay={0.3}
                      onChange={(value) => handleFieldChange('phone', value)}
                      onBlur={() => handleFieldBlur('phone')}
                    />

                    <FormField
                      id="company"
                      name="company"
                      label="Empresa"
                      type="text"
                      placeholder="Nombre de tu empresa"
                      value={formData.company}
                      touched={touched.company}
                      icon={<Building className="w-4 h-4" />}
                      delay={0.4}
                      onChange={(value) => handleFieldChange('company', value)}
                      onBlur={() => handleFieldBlur('company')}
                    />
                  </div>

                  {/* Servicio (hidden field) */}
                  <input type="hidden" name="service" value={service} />

                  {/* Presupuesto y Timeline */}
                  {(showBudget || showTimeline) && (
                    <div className="grid md:grid-cols-2 gap-6">
                      {showBudget && (
                        <motion.div 
                          className="space-y-2"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.5 }}
                        >
                          <label className="text-white font-medium flex items-center gap-2">
                            <DollarSign className="w-4 h-4" />
                            Presupuesto estimado
                          </label>
                          <Select 
                            name="budget" 
                            value={formData.budget}
                            onValueChange={(value) => handleFieldChange('budget', value)}
                          >
                            <SelectTrigger className="bg-slate-800/50 border-slate-600">
                              <SelectValue placeholder="Selecciona un rango" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1000-5000">$1,000 - $5,000</SelectItem>
                              <SelectItem value="5000-10000">$5,000 - $10,000</SelectItem>
                              <SelectItem value="10000-25000">$10,000 - $25,000</SelectItem>
                              <SelectItem value="25000+">$25,000+</SelectItem>
                            </SelectContent>
                          </Select>
                        </motion.div>
                      )}

                      {showTimeline && (
                        <motion.div 
                          className="space-y-2"
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.6 }}
                        >
                          <label className="text-white font-medium flex items-center gap-2">
                            <Calendar className="w-4 h-4" />
                            Timeline del proyecto
                          </label>
                          <Select 
                            name="timeline"
                            value={formData.timeline}
                            onValueChange={(value) => handleFieldChange('timeline', value)}
                          >
                            <SelectTrigger className="bg-slate-800/50 border-slate-600">
                              <SelectValue placeholder="¿Cuándo necesitas comenzar?" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="inmediato">
                                <div className="flex items-center gap-2">
                                  <Zap className="w-4 h-4 text-orange-500" />
                                  Inmediatamente
                                </div>
                              </SelectItem>
                              <SelectItem value="1-2-semanas">1-2 semanas</SelectItem>
                              <SelectItem value="1-mes">En 1 mes</SelectItem>
                              <SelectItem value="2-3-meses">2-3 meses</SelectItem>
                              <SelectItem value="flexible">Flexible</SelectItem>
                            </SelectContent>
                          </Select>
                        </motion.div>
                      )}
                    </div>
                  )}

                  <FormField
                    id="message"
                    name="message"
                    label="Cuéntanos sobre tu proyecto"
                    type="textarea"
                    placeholder="Describe tu proyecto, objetivos y cualquier detalle importante..."
                    required
                    value={formData.message}
                    error={fieldErrors.message || state.errors?.message?.[0]}
                    touched={touched.message}
                    icon={<MessageSquare className="w-4 h-4" />}
                    maxLength={500}
                    rows={4}
                    delay={0.7}
                    onChange={(value) => handleFieldChange('message', value)}
                    onBlur={() => handleFieldBlur('message')}
                  />

                  {/* Botón de envío */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                  >
                    <motion.div
                      whileHover={{ scale: isPending || !isFormValid() ? 1 : 1.02 }}
                      whileTap={{ scale: isPending || !isFormValid() ? 1 : 0.98 }}
                    >
                      <Button
                        type="submit"
                        disabled={isPending || !isFormValid()}
                        size="lg"
                        className={`w-full transition-all duration-300 ${
                          isFormValid()
                            ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                            : 'bg-slate-600 cursor-not-allowed'
                        }`}
                      >
                      {isPending ? (
                        <div className="flex items-center space-x-2">
                          <motion.div 
                            className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          />
                          <span>Enviando consulta...</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <Send className="w-5 h-5" />
                          <span>Enviar Consulta</span>
                          {isFormValid() && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="w-2 h-2 bg-green-400 rounded-full"
                            />
                          )}
                        </div>
                      )}
                    </Button>
                    </motion.div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.9 }}
                    className="space-y-2"
                  >
                    <p className="text-slate-400 text-sm text-center font-light">
                      * Campos obligatorios. Te contactaremos en menos de 24 horas.
                    </p>
                    {!isFormValid() && (
                      <p className="text-amber-400 text-xs text-center flex items-center justify-center gap-1">
                        <AlertCircle className="w-3 h-3" />
                        Completa todos los campos requeridos para enviar
                      </p>
                    )}
                  </motion.div>
                </>
              )}
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </>
  )
}
