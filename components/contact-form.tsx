"use client"

import { useActionState, useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { AlertCircle, CheckCircle, Send, Mail, Phone, Clock, Zap, User, Building, MessageSquare, DollarSign, Calendar } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { submitContactFormWithFormData, type ContactFormState } from "@/lib/contact-actions"

interface ContactFormProps {
  service: string
  title?: string
  description?: string
  showBudget?: boolean
  showTimeline?: boolean
}

const initialState: ContactFormState = {}

// Validación en tiempo real - España
const validateField = (name: string, value: string) => {
  switch (name) {
    case 'name':
      return value.length < 2 ? 'El nombre debe tener al menos 2 caracteres' : ''
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return !emailRegex.test(value) ? 'Introduce un email válido' : ''
    case 'phone':
      // Teléfono opcional - solo validar si se proporciona
      if (value.trim() === '') return '' // Permitir vacío
      const phoneDigits = value.replace(/\D/g, '')
      return phoneDigits.length < 9 ? 'El teléfono debe tener al menos 9 dígitos' : ''
    case 'message':
      return value.trim().length < 10 ? 'El mensaje debe tener al menos 10 caracteres' : ''
    default:
      return ''
  }
}

// Formatear teléfono automáticamente - España
const formatPhone = (value: string) => {
  const digits = value.replace(/\D/g, '')

  if (digits.length === 0) return ''
  if (digits.length <= 3) return digits
  if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`
  if (digits.length <= 9) return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`

  // Si empieza con 34 (código de España), formatear como +34 XXX XXX XXX
  if (digits.startsWith('34') && digits.length >= 11) {
    return `+34 ${digits.slice(2, 5)} ${digits.slice(5, 8)} ${digits.slice(8, 11)}`
  }

  // Formato estándar español (9 dígitos)
  return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`
}

export default function ContactForm({
  service,
  title = "¿Listo para comenzar?",
  description = "Completa el formulario y te contactaremos en menos de 24 horas",
  showBudget = false,
  showTimeline = false,
}: ContactFormProps) {
  const [state, formAction, isPending] = useActionState(submitContactFormWithFormData, initialState)

  // Estados para validación en tiempo real
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
    budget: '',
    timeline: ''
  })

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  // Manejar cambios en los campos
  const handleFieldChange = (name: string, value: string) => {
    let processedValue = value

    // Formatear teléfono automáticamente
    if (name === 'phone') {
      processedValue = formatPhone(value)
    }

    setFormData(prev => ({ ...prev, [name]: processedValue }))

    // Validar solo si el campo ha sido tocado
    if (touched[name]) {
      const error = validateField(name, value)
      setFieldErrors(prev => ({ ...prev, [name]: error }))
    }
  }

  // Manejar cuando un campo pierde el foco
  const handleFieldBlur = (name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }))
    const error = validateField(name, formData[name])
    setFieldErrors(prev => ({ ...prev, [name]: error }))
  }

  // Verificar si el formulario es válido - Teléfono opcional
  const isFormValid = () => {
    const requiredFields = ['name', 'email', 'message'] // Teléfono ya no es obligatorio
    const requiredFieldsValid = requiredFields.every(field =>
      formData[field].trim() !== '' && !fieldErrors[field]
    )

    // Verificar que no haya errores en campos opcionales (como teléfono)
    const noFieldErrors = Object.keys(fieldErrors).every(field => !fieldErrors[field])

    return requiredFieldsValid && noFieldErrors
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
    >
      <Card className="max-w-2xl mx-auto">
        <CardHeader className="p-8">
          <CardTitle className="text-2xl font-bold text-white tracking-tight">{title}</CardTitle>
          <CardDescription className="text-slate-400 font-light text-lg">{description}</CardDescription>

          {/* Service badge */}
          <div className="flex items-center gap-2 mt-4">
            <Badge variant="tech" className="text-sm">
              <Zap className="w-3 h-3 mr-1" />
              {service}
            </Badge>
            <Badge variant="success" className="text-sm">
              <Clock className="w-3 h-3 mr-1" />
              Respuesta en 24h
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-8 pt-0">
          <form action={formAction} className="space-y-6">
            {/* Mensaje de estado */}
            {state.message && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className={`p-6 rounded-2xl flex items-start space-x-4 ${
                  state.success
                    ? "bg-green-600/20 border border-green-600/30 text-green-400"
                    : "bg-red-600/20 border border-red-600/30 text-red-400"
                }`}
              >
                <div className="flex-shrink-0 mt-1">
                  {state.success ? <CheckCircle className="w-5 h-5" /> : <AlertCircle className="w-5 h-5" />}
                </div>
                <div className="flex-1">
                  <div className="font-medium mb-2">
                    {state.success ? "¡Consulta enviada exitosamente!" : "Error al enviar consulta"}
                  </div>
                  <div className="text-sm leading-relaxed whitespace-pre-line">{state.message}</div>

                  {state.success && state.emailSent && (
                    <div className="mt-4 pt-4 border-t border-green-600/30">
                      <div className="text-sm font-medium mb-2">📧 Revisa tu email para más detalles</div>
                      <div className="flex flex-col sm:flex-row gap-2">
                        <a
                          href="mailto:<EMAIL>"
                          className="inline-flex items-center text-sm text-green-300 hover:text-green-200 transition-colors"
                        >
                          <Mail className="w-4 h-4 mr-2" />
                          <EMAIL>
                        </a>
                        <a
                          href="tel:+15551234567"
                          className="inline-flex items-center text-sm text-green-300 hover:text-green-200 transition-colors"
                        >
                          <Phone className="w-4 h-4 mr-2" />
                          +****************
                        </a>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            )}

            {/* Solo mostrar el formulario si no se ha enviado exitosamente */}
            {!state.success && (
              <>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Nombre */}
                  <motion.div
                    className="space-y-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <Label htmlFor="name" className="text-white font-medium flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Nombre completo *
                    </Label>
                    <div className="relative">
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        required
                        placeholder="Tu nombre completo"
                        value={formData.name}
                        onChange={(e) => handleFieldChange('name', e.target.value)}
                        onBlur={() => handleFieldBlur('name')}
                        className={`transition-all duration-200 ${
                          touched.name && fieldErrors.name
                            ? 'border-red-500 focus:border-red-500'
                            : touched.name && !fieldErrors.name
                            ? 'border-green-500 focus:border-green-500'
                            : ''
                        }`}
                      />
                      {touched.name && !fieldErrors.name && (
                        <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
                      )}
                    </div>
                    <AnimatePresence>
                      {(fieldErrors.name || state.errors?.name) && (
                        <motion.p
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="text-red-400 text-sm flex items-center gap-1"
                        >
                          <AlertCircle className="w-3 h-3" />
                          {fieldErrors.name || state.errors?.name?.[0]}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  {/* Email */}
                  <motion.div
                    className="space-y-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <Label htmlFor="email" className="text-white font-medium flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      Email *
                    </Label>
                    <div className="relative">
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        required
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => handleFieldChange('email', e.target.value)}
                        onBlur={() => handleFieldBlur('email')}
                        className={`transition-all duration-200 ${
                          touched.email && fieldErrors.email
                            ? 'border-red-500 focus:border-red-500'
                            : touched.email && !fieldErrors.email
                            ? 'border-green-500 focus:border-green-500'
                            : ''
                        }`}
                      />
                      {touched.email && !fieldErrors.email && formData.email && (
                        <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
                      )}
                    </div>
                    <AnimatePresence>
                      {(fieldErrors.email || state.errors?.email) && (
                        <motion.p
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="text-red-400 text-sm flex items-center gap-1"
                        >
                          <AlertCircle className="w-3 h-3" />
                          {fieldErrors.email || state.errors?.email?.[0]}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </motion.div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  {/* Teléfono */}
                  <motion.div
                    className="space-y-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <Label htmlFor="phone" className="text-white font-medium flex items-center gap-2">
                      <Phone className="w-4 h-4" />
                      Teléfono
                    </Label>
                    <div className="relative">
                      <Input
                        id="phone"
                        name="phone"
                        type="tel"
                        placeholder="123 456 789"
                        value={formData.phone}
                        onChange={(e) => handleFieldChange('phone', e.target.value)}
                        onBlur={() => handleFieldBlur('phone')}
                        className={`transition-all duration-200 ${
                          touched.phone && fieldErrors.phone
                            ? 'border-red-500 focus:border-red-500'
                            : touched.phone && !fieldErrors.phone && formData.phone
                            ? 'border-green-500 focus:border-green-500'
                            : ''
                        }`}
                      />
                      {touched.phone && !fieldErrors.phone && formData.phone && (
                        <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
                      )}
                    </div>
                    <AnimatePresence>
                      {(fieldErrors.phone || state.errors?.phone) && (
                        <motion.p
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="text-red-400 text-sm flex items-center gap-1"
                        >
                          <AlertCircle className="w-3 h-3" />
                          {fieldErrors.phone || state.errors?.phone?.[0]}
                        </motion.p>
                      )}
                    </AnimatePresence>
                  </motion.div>

                  {/* Empresa */}
                  <motion.div
                    className="space-y-2"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Label htmlFor="company" className="text-white font-medium flex items-center gap-2">
                      <Building className="w-4 h-4" />
                      Empresa
                    </Label>
                    <Input
                      id="company"
                      name="company"
                      type="text"
                      placeholder="Nombre de tu empresa"
                      value={formData.company}
                      onChange={(e) => handleFieldChange('company', e.target.value)}
                    />
                  </motion.div>
                </div>

                {/* Servicio (hidden field con valor predeterminado) */}
                <input type="hidden" name="service" value={service} />

                {/* Presupuesto y Timeline si se requieren */}
                {(showBudget || showTimeline) && (
                  <div className="grid md:grid-cols-2 gap-6">
                    {showBudget && (
                      <motion.div
                        className="space-y-2"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.6 }}
                      >
                        <Label htmlFor="budget" className="text-white font-medium flex items-center gap-2">
                          <DollarSign className="w-4 h-4" />
                          Presupuesto estimado
                        </Label>
                        <Select
                          name="budget"
                          value={formData.budget}
                          onValueChange={(value) => handleFieldChange('budget', value)}
                        >
                          <SelectTrigger className="bg-slate-800/50 border-slate-600">
                            <SelectValue placeholder="Selecciona un rango" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1000-5000">$1,000 - $5,000</SelectItem>
                            <SelectItem value="5000-10000">$5,000 - $10,000</SelectItem>
                            <SelectItem value="10000-25000">$10,000 - $25,000</SelectItem>
                            <SelectItem value="25000+">$25,000+</SelectItem>
                          </SelectContent>
                        </Select>
                      </motion.div>
                    )}

                    {showTimeline && (
                      <motion.div
                        className="space-y-2"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.7 }}
                      >
                        <Label htmlFor="timeline" className="text-white font-medium flex items-center gap-2">
                          <Calendar className="w-4 h-4" />
                          Timeline del proyecto
                        </Label>
                        <Select
                          name="timeline"
                          value={formData.timeline}
                          onValueChange={(value) => handleFieldChange('timeline', value)}
                        >
                          <SelectTrigger className="bg-slate-800/50 border-slate-600">
                            <SelectValue placeholder="¿Cuándo necesitas comenzar?" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="inmediato">
                              <div className="flex items-center gap-2">
                                <Zap className="w-4 h-4 text-orange-500" />
                                Inmediatamente
                              </div>
                            </SelectItem>
                            <SelectItem value="1-2-semanas">1-2 semanas</SelectItem>
                            <SelectItem value="1-mes">En 1 mes</SelectItem>
                            <SelectItem value="2-3-meses">2-3 meses</SelectItem>
                            <SelectItem value="flexible">Flexible</SelectItem>
                          </SelectContent>
                        </Select>
                      </motion.div>
                    )}
                  </div>
                )}

                {/* Mensaje */}
                <motion.div
                  className="space-y-2"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Label htmlFor="message" className="text-white font-medium flex items-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    Cuéntanos sobre tu proyecto *
                  </Label>
                  <div className="relative">
                    <Textarea
                      id="message"
                      name="message"
                      required
                      rows={4}
                      minLength={10}
                      maxLength={500}
                      placeholder="Describe tu proyecto, objetivos y cualquier detalle importante..."
                      className={`resize-none transition-all duration-200 ${
                        touched.message && fieldErrors.message
                          ? 'border-red-500 focus:border-red-500'
                          : touched.message && !fieldErrors.message
                          ? 'border-green-500 focus:border-green-500'
                          : ''
                      }`}
                      value={formData.message}
                      onChange={(e) => handleFieldChange('message', e.target.value)}
                      onBlur={() => handleFieldBlur('message')}
                    />
                    <div className="absolute bottom-3 right-3 text-xs text-slate-400">
                      {formData.message.length}/500
                    </div>
                  </div>
                  <AnimatePresence>
                    {(fieldErrors.message || state.errors?.message) && (
                      <motion.p
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="text-red-400 text-sm flex items-center gap-1"
                      >
                        <AlertCircle className="w-3 h-3" />
                        {fieldErrors.message || state.errors?.message?.[0]}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </motion.div>

                {/* Botón de envío */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                >
                  <motion.div
                    whileHover={{ scale: isPending || !isFormValid() ? 1 : 1.02 }}
                    whileTap={{ scale: isPending || !isFormValid() ? 1 : 0.98 }}
                  >
                    <Button
                      type="submit"
                      disabled={isPending || !isFormValid()}
                      size="lg"
                      className={`w-full transition-all duration-300 ${
                        isFormValid()
                          ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                          : 'bg-slate-600 cursor-not-allowed'
                      }`}
                    >
                    {isPending ? (
                      <div className="flex items-center space-x-2">
                        <motion.div
                          className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                        <span>Enviando consulta...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2">
                        <Send className="w-5 h-5" />
                        <span>Enviar Consulta</span>
                        {isFormValid() && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="w-2 h-2 bg-green-400 rounded-full"
                          />
                        )}
                      </div>
                    )}
                  </Button>
                  </motion.div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.9 }}
                  className="space-y-2"
                >
                  <p className="text-slate-400 text-sm text-center font-light">
                    * Campos obligatorios. Te contactaremos en menos de 24 horas.
                  </p>
                  {!isFormValid() && (
                    <p className="text-amber-400 text-xs text-center flex items-center justify-center gap-1">
                      <AlertCircle className="w-3 h-3" />
                      Completa todos los campos requeridos para enviar
                    </p>
                  )}
                </motion.div>
              </>
            )}
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}
