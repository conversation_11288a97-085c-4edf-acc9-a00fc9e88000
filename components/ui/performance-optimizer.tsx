"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function PerformanceOptimizer() {
  const router = useRouter()

  useEffect(() => {
    // Preload critical routes
    const criticalRoutes = [
      '/servicios/consultoria-ia',
      '/servicios/creacion-web',
      '/servicios/analisis-vulnerabilidades',
      '/servicios/asistencia-informatica'
    ]

    // Preload routes after initial load
    const preloadTimer = setTimeout(() => {
      criticalRoutes.forEach(route => {
        router.prefetch(route)
      })
    }, 2000)

    // Preload critical images
    const criticalImages = [
      '/logo.png',
      '/favicon.ico',
      '/apple-touch-icon.png'
    ]

    criticalImages.forEach(src => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = src
      if (!document.querySelector(`link[href="${src}"]`)) {
        document.head.appendChild(link)
      }
    })

    // Optimize third-party scripts loading
    const optimizeThirdParty = () => {
      // Defer non-critical scripts
      const scripts = document.querySelectorAll('script[src]')
      scripts.forEach(script => {
        if (!script.hasAttribute('async') && !script.hasAttribute('defer')) {
          script.setAttribute('defer', '')
        }
      })
    }

    // Service Worker registration for caching
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js')
          console.log('SW registered: ', registration)
        } catch (registrationError) {
          console.log('SW registration failed: ', registrationError)
        }
      }
    }

    // Intersection Observer for lazy loading
    const setupIntersectionObserver = () => {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target as HTMLImageElement
              if (img.dataset.src) {
                img.src = img.dataset.src
                img.classList.remove('lazy')
                observer.unobserve(img)
              }
            }
          })
        })

        // Observe all lazy images
        document.querySelectorAll('img[data-src]').forEach(img => {
          imageObserver.observe(img)
        })
      }
    }

    // Resource hints for better performance
    const addResourceHints = () => {
      const hints = [
        { rel: 'dns-prefetch', href: '//fonts.googleapis.com' },
        { rel: 'dns-prefetch', href: '//fonts.gstatic.com' },
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: 'anonymous' }
      ]

      hints.forEach(hint => {
        const link = document.createElement('link')
        link.rel = hint.rel
        link.href = hint.href
        if (hint.crossorigin) link.crossOrigin = hint.crossorigin
        
        if (!document.querySelector(`link[rel="${hint.rel}"][href="${hint.href}"]`)) {
          document.head.appendChild(link)
        }
      })
    }

    // Memory management
    const optimizeMemory = () => {
      // Clean up unused event listeners
      const cleanupTimer = setInterval(() => {
        // Force garbage collection if available (dev only)
        if (process.env.NODE_ENV === 'development' && window.gc) {
          window.gc()
        }
      }, 30000)

      return () => clearInterval(cleanupTimer)
    }

    // Performance monitoring
    const monitorPerformance = () => {
      if ('PerformanceObserver' in window) {
        // Monitor Largest Contentful Paint
        const lcpObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          const lastEntry = entries[entries.length - 1]
          console.log('LCP:', lastEntry.startTime)
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

        // Monitor First Input Delay
        const fidObserver = new PerformanceObserver((entryList) => {
          const entries = entryList.getEntries()
          entries.forEach(entry => {
            console.log('FID:', entry.processingStart - entry.startTime)
          })
        })
        fidObserver.observe({ entryTypes: ['first-input'] })

        // Monitor Cumulative Layout Shift
        const clsObserver = new PerformanceObserver((entryList) => {
          let clsValue = 0
          const entries = entryList.getEntries()
          entries.forEach(entry => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          console.log('CLS:', clsValue)
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      }
    }

    // Execute optimizations
    optimizeThirdParty()
    addResourceHints()
    setupIntersectionObserver()
    registerServiceWorker()
    const memoryCleanup = optimizeMemory()
    
    if (process.env.NODE_ENV === 'development') {
      monitorPerformance()
    }

    // Cleanup
    return () => {
      clearTimeout(preloadTimer)
      memoryCleanup()
    }
  }, [router])

  // Prefetch on hover for better UX
  useEffect(() => {
    const handleMouseEnter = (e: MouseEvent) => {
      const target = e.target as HTMLElement
      const link = target.closest('a[href^="/"]') as HTMLAnchorElement
      if (link && link.href) {
        const url = new URL(link.href)
        if (url.pathname !== window.location.pathname) {
          router.prefetch(url.pathname)
        }
      }
    }

    document.addEventListener('mouseenter', handleMouseEnter, true)
    return () => document.removeEventListener('mouseenter', handleMouseEnter, true)
  }, [router])

  return null
}
