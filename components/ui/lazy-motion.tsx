"use client"

import dynamic from 'next/dynamic'
import { ComponentProps } from 'react'

// Lazy load Framer Motion components
export const LazyMotionDiv = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.div })),
  {
    ssr: false,
    loading: () => <div className="opacity-0" />
  }
)

export const LazyMotionSection = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.section })),
  {
    ssr: false,
    loading: () => <section className="opacity-0" />
  }
)

export const LazyMotionHeader = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.header })),
  {
    ssr: false,
    loading: () => <header className="opacity-0" />
  }
)

export const LazyMotionFooter = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.motion.footer })),
  {
    ssr: false,
    loading: () => <footer className="opacity-0" />
  }
)

export const LazyAnimatePresence = dynamic(
  () => import('framer-motion').then(mod => ({ default: mod.AnimatePresence })),
  {
    ssr: false,
    loading: () => <div />
  }
)

// Hook para lazy loading de useScroll y useTransform
export const useLazyMotionHooks = () => {
  const useScrollLazy = dynamic(
    () => import('framer-motion').then(mod => mod.useScroll),
    { ssr: false }
  )
  
  const useTransformLazy = dynamic(
    () => import('framer-motion').then(mod => mod.useTransform),
    { ssr: false }
  )
  
  const useInViewLazy = dynamic(
    () => import('framer-motion').then(mod => mod.useInView),
    { ssr: false }
  )
  
  return { useScrollLazy, useTransformLazy, useInViewLazy }
}

// Tipos para mejor TypeScript support
export type LazyMotionDivProps = ComponentProps<typeof LazyMotionDiv>
export type LazyMotionSectionProps = ComponentProps<typeof LazyMotionSection>
export type LazyMotionHeaderProps = ComponentProps<typeof LazyMotionHeader>
export type LazyMotionFooterProps = ComponentProps<typeof LazyMotionFooter>
