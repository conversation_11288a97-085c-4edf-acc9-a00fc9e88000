"use client"

import { ReactNode } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, CheckCircle } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface FormFieldProps {
  id: string
  name: string
  label: string
  type?: 'text' | 'email' | 'tel' | 'textarea'
  placeholder?: string
  required?: boolean
  value: string
  error?: string
  touched?: boolean
  icon?: ReactNode
  maxLength?: number
  rows?: number
  delay?: number
  onChange: (value: string) => void
  onBlur: () => void
}

export function FormField({
  id,
  name,
  label,
  type = 'text',
  placeholder,
  required = false,
  value,
  error,
  touched,
  icon,
  maxLength,
  rows = 4,
  delay = 0,
  onChange,
  onBlur
}: FormFieldProps) {
  const hasError = touched && error
  const hasSuccess = touched && !error && value.trim() !== ''

  const inputClassName = `transition-all duration-200 ${
    hasError 
      ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' 
      : hasSuccess 
      ? 'border-green-500 focus:border-green-500 focus:ring-green-500/20' 
      : 'focus:border-blue-500 focus:ring-blue-500/20'
  }`

  return (
    <motion.div 
      className="space-y-2"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay }}
    >
      <Label htmlFor={id} className="text-white font-medium flex items-center gap-2">
        {icon}
        {label} {required && '*'}
      </Label>
      
      <div className="relative">
        {type === 'textarea' ? (
          <Textarea
            id={id}
            name={name}
            required={required}
            placeholder={placeholder}
            className={`resize-none ${inputClassName}`}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            rows={rows}
            maxLength={maxLength}
          />
        ) : (
          <Input
            id={id}
            name={name}
            type={type}
            required={required}
            placeholder={placeholder}
            className={inputClassName}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onBlur={onBlur}
            maxLength={maxLength}
          />
        )}
        
        {/* Success indicator */}
        {hasSuccess && (
          <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
        )}
        
        {/* Character counter for textarea */}
        {type === 'textarea' && maxLength && (
          <div className="absolute bottom-3 right-3 text-xs text-slate-400">
            {value.length}/{maxLength}
          </div>
        )}
      </div>
      
      {/* Error message */}
      <AnimatePresence>
        {hasError && (
          <motion.p 
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="text-red-400 text-sm flex items-center gap-1"
          >
            <AlertCircle className="w-3 h-3" />
            {error}
          </motion.p>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
