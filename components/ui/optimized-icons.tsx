"use client"

import dynamic from 'next/dynamic'
import { LucideProps } from 'lucide-react'

// Lazy load solo los iconos que realmente necesitamos
export const ArrowRightIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.ArrowRight })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const CheckCircleIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.CheckCircle })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const WrenchIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Wrench })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const ShieldIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Shield })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const BrainIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Brain })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const CodeIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Code })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const MailIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Mail })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const PhoneIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Phone })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const MapPinIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.MapPin })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const MenuIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Menu })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const XIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.X })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const StarIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Star })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const ChevronDownIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.ChevronDown })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const GlobeIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Globe })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const BugIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Bug })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const UserIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.User })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const ZapIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Zap })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

// Admin icons
export const UsersIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Users })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const TrendingUpIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.TrendingUp })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const BellIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Bell })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const SearchIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Search })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const SettingsIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.Settings })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

export const LogOutIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.LogOut })), {
  ssr: false,
  loading: () => <div className="w-4 h-4 bg-gray-200 animate-pulse rounded" />
})

// Tipo para props de iconos
export type OptimizedIconProps = LucideProps
