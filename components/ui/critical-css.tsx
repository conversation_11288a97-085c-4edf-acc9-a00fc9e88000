"use client"

import { useEffect } from 'react'

// CSS crítico inline para mejorar el First Contentful Paint
const criticalCSS = `
  /* Critical CSS for above-the-fold content */
  .bg-hero-gradient {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }
  
  .text-brand-primary {
    color: #5C9EAD;
  }
  
  .text-brand-secondary {
    color: #4A8A9B;
  }
  
  .text-brand-accent {
    color: #6EAFC0;
  }
  
  /* Loading spinner optimization */
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  /* Critical layout styles */
  .container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  @media (min-width: 640px) {
    .container {
      max-width: 640px;
    }
  }
  
  @media (min-width: 768px) {
    .container {
      max-width: 768px;
    }
  }
  
  @media (min-width: 1024px) {
    .container {
      max-width: 1024px;
    }
  }
  
  @media (min-width: 1280px) {
    .container {
      max-width: 1280px;
    }
  }
  
  @media (min-width: 1536px) {
    .container {
      max-width: 1536px;
    }
  }
  
  /* Critical button styles */
  .btn-primary {
    background: linear-gradient(135deg, #5C9EAD 0%, #4A8A9B 100%);
    color: white;
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
  }
  
  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(92, 158, 173, 0.3);
  }
  
  /* Critical navigation styles */
  .nav-fixed {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 50;
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
  }
  
  /* Critical text styles */
  .text-white { color: #ffffff; }
  .text-slate-300 { color: #cbd5e1; }
  .text-slate-400 { color: #94a3b8; }
  .text-slate-500 { color: #64748b; }
  .text-slate-600 { color: #475569; }
  .text-slate-700 { color: #334155; }
  .text-slate-800 { color: #1e293b; }
  .text-slate-900 { color: #0f172a; }
  
  /* Critical background styles */
  .bg-slate-800 { background-color: #1e293b; }
  .bg-slate-900 { background-color: #0f172a; }
  
  /* Critical spacing */
  .p-4 { padding: 1rem; }
  .p-6 { padding: 1.5rem; }
  .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
  .px-4 { padding-left: 1rem; padding-right: 1rem; }
  .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .mb-4 { margin-bottom: 1rem; }
  .mb-6 { margin-bottom: 1.5rem; }
  .mb-8 { margin-bottom: 2rem; }
  
  /* Critical flexbox */
  .flex { display: flex; }
  .items-center { align-items: center; }
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }
  
  /* Critical positioning */
  .relative { position: relative; }
  .absolute { position: absolute; }
  .fixed { position: fixed; }
  
  /* Critical sizing */
  .w-full { width: 100%; }
  .h-full { height: 100%; }
  .min-h-screen { min-height: 100vh; }
  
  /* Critical opacity */
  .opacity-0 { opacity: 0; }
  .opacity-100 { opacity: 1; }
  
  /* Critical transforms */
  .translate-y-2 { transform: translateY(0.5rem); }
  .scale-105 { transform: scale(1.05); }
`

export default function CriticalCSS() {
  useEffect(() => {
    // Inject critical CSS if not already present
    if (!document.getElementById('critical-css')) {
      const style = document.createElement('style')
      style.id = 'critical-css'
      style.textContent = criticalCSS
      document.head.appendChild(style)
    }
    
    // Preload important resources
    const preloadLinks = [
      { href: '/logo.png', as: 'image' },
      { href: '/favicon.ico', as: 'image' },
    ]
    
    preloadLinks.forEach(({ href, as }) => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.href = href
      link.as = as
      if (!document.querySelector(`link[href="${href}"]`)) {
        document.head.appendChild(link)
      }
    })
  }, [])
  
  return null
}
