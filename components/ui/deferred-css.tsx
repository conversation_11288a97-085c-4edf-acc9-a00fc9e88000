"use client"

import { useEffect } from 'react'

// CSS no crítico que se puede cargar después del First Contentful Paint
const deferredCSS = `
  /* Service Color Classes - Loaded after critical render */
  .service-tech {
    color: rgb(34 197 94);
    background-color: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.3);
  }

  .service-security {
    color: rgb(239 68 68);
    background-color: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.3);
  }

  .service-ai {
    color: rgb(168 85 247);
    background-color: rgba(168, 85, 247, 0.2);
    border-color: rgba(168, 85, 247, 0.3);
  }

  .service-web {
    color: rgb(59 130 246);
    background-color: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
  }

  /* Advanced animations - Deferred */
  @keyframes bounce-gentle {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px rgba(20, 184, 166, 0.3); }
    50% { box-shadow: 0 0 20px rgba(20, 184, 166, 0.6); }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
  }

  .animate-float {
    animation: bounce-gentle 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .shake {
    animation: shake 0.5s ease-in-out;
  }

  /* Scrollbar styling - Non-critical */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #14b8a6 #1e293b;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #1e293b;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #14b8a6;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #0d9488;
  }

  /* Form enhancements - Deferred */
  .form-field-success {
    border-color: rgb(34 197 94);
  }

  .form-field-success:focus {
    border-color: rgb(34 197 94);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
  }

  .form-field-error {
    border-color: rgb(239 68 68);
  }

  .form-field-error:focus {
    border-color: rgb(239 68 68);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
  }

  .floating-label {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgb(148 163 184);
    transition: all 0.2s;
    pointer-events: none;
  }

  .floating-label.active {
    top: 0;
    left: 0.5rem;
    font-size: 0.75rem;
    background-color: rgb(15 23 42);
    padding: 0 0.25rem;
    color: rgb(59 130 246);
  }

  /* Background patterns - Deferred */
  .bg-grid {
    background-image: linear-gradient(rgba(20, 184, 166, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(20, 184, 166, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .bg-dots {
    background-image: radial-gradient(rgba(20, 184, 166, 0.2) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Selection styling - Deferred */
  ::selection {
    background-color: rgba(20, 184, 166, 0.3);
    color: white;
  }

  /* Print styles - Deferred */
  @media print {
    .no-print {
      display: none !important;
    }
  }

  /* Advanced hover effects - Deferred */
  .card-tech:hover {
    border-color: rgba(34, 197, 94, 0.5);
    box-shadow: 0 10px 15px -3px rgba(34, 197, 94, 0.1);
  }

  .card-security:hover {
    border-color: rgba(239, 68, 68, 0.5);
    box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.1);
  }

  .card-ai:hover {
    border-color: rgba(168, 85, 247, 0.5);
    box-shadow: 0 10px 15px -3px rgba(168, 85, 247, 0.1);
  }

  .card-web:hover {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.1);
  }
`

export default function DeferredCSS() {
  useEffect(() => {
    // Load deferred CSS after initial render
    const timer = setTimeout(() => {
      if (!document.getElementById('deferred-css')) {
        const style = document.createElement('style')
        style.id = 'deferred-css'
        style.textContent = deferredCSS
        document.head.appendChild(style)
      }
    }, 100) // Small delay to ensure critical render is complete

    return () => clearTimeout(timer)
  }, [])

  return null
}
