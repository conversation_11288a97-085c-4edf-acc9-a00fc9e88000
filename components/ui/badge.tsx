import type * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        tech: "border-transparent bg-service-tech-500/20 text-service-tech-400 hover:bg-service-tech-500/30",
        security:
          "border-transparent bg-service-security-500/20 text-service-security-400 hover:bg-service-security-500/30",
        ai: "border-transparent bg-service-ai-500/20 text-service-ai-400 hover:bg-service-ai-500/30",
        web: "border-transparent bg-service-web-500/20 text-service-web-400 hover:bg-service-web-500/30",
        success: "border-transparent bg-green-600/20 text-green-400 hover:bg-green-600/30",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
)

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return <div className={cn(badgeVariants({ variant }), className)} {...props} />
}

export { Badge, badgeVariants }
