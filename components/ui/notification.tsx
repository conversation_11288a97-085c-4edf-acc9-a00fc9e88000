"use client"

import { useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { CheckCircle, AlertCircle, X, Mail, Clock } from "lucide-react"

interface NotificationProps {
  show: boolean
  type: 'success' | 'error' | 'info'
  title: string
  message: string
  onClose: () => void
  autoClose?: boolean
  duration?: number
}

export function Notification({
  show,
  type,
  title,
  message,
  onClose,
  autoClose = true,
  duration = 5000
}: NotificationProps) {
  useEffect(() => {
    if (show && autoClose) {
      const timer = setTimeout(() => {
        onClose()
      }, duration)
      return () => clearTimeout(timer)
    }
  }, [show, autoClose, duration, onClose])

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-500" />
      case 'error':
        return <AlertCircle className="w-6 h-6 text-red-500" />
      case 'info':
        return <Mail className="w-6 h-6 text-blue-500" />
      default:
        return <CheckCircle className="w-6 h-6 text-green-500" />
    }
  }

  const getBgColor = () => {
    switch (type) {
      case 'success':
        return 'bg-green-900/90 border-green-500/50'
      case 'error':
        return 'bg-red-900/90 border-red-500/50'
      case 'info':
        return 'bg-blue-900/90 border-blue-500/50'
      default:
        return 'bg-green-900/90 border-green-500/50'
    }
  }

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ opacity: 0, y: -100, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -100, scale: 0.9 }}
          className="fixed top-4 right-4 z-50 max-w-md"
        >
          <div className={`${getBgColor()} border rounded-2xl p-4 backdrop-blur-sm shadow-2xl`}>
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0">
                {getIcon()}
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-white font-semibold text-sm">
                  {title}
                </h4>
                <p className="text-slate-300 text-sm mt-1 leading-relaxed">
                  {message}
                </p>
                
                {type === 'success' && (
                  <div className="flex items-center gap-2 mt-2 text-xs text-slate-400">
                    <Clock className="w-3 h-3" />
                    Te contactaremos pronto
                  </div>
                )}
              </div>
              
              <button
                onClick={onClose}
                className="flex-shrink-0 text-slate-400 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Progress bar for auto-close */}
            {autoClose && (
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-white/20 rounded-b-2xl"
                initial={{ width: "100%" }}
                animate={{ width: "0%" }}
                transition={{ duration: duration / 1000, ease: "linear" }}
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
