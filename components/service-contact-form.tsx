"use client"

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Badge } from '@/components/ui/badge'
import { submitContactForm, submitContactFormSimple, submitContactFormSync, submitContactFormMCP } from '@/lib/contact-actions'
import { 
  Send, 
  CheckCircle, 
  AlertCircle, 
  Globe, 
  Shield, 
  Brain, 
  Wrench,
  Clock,
  Euro,
  Users,
  Zap
} from 'lucide-react'

interface ServiceContactFormProps {
  serviceType: 'web' | 'security' | 'ai' | 'support'
  serviceName: string
  serviceDescription: string
}

const serviceConfigs = {
  web: {
    icon: Globe,
    color: 'from-blue-600 to-purple-600',
    fields: [
      {
        id: 'projectType',
        label: 'Tipo de Proyecto Web',
        type: 'select',
        required: true,
        options: [
          'Página web corporativa',
          'Tienda online (E-commerce)',
          'Aplicación web personalizada',
          'Landing page',
          'Blog o portal de contenidos',
          'Plataforma educativa',
          'Sistema de gestión (CRM/ERP)',
          'Otro'
        ]
      },
      {
        id: 'features',
        label: 'Funcionalidades Requeridas',
        type: 'checkbox',
        options: [
          'Diseño responsive (móvil/tablet)',
          'Sistema de pagos online',
          'Gestión de usuarios/clientes',
          'Panel de administración',
          'Integración con redes sociales',
          'SEO optimizado',
          'Multiidioma',
          'Chat en vivo',
          'Newsletter/Email marketing',
          'Analytics y reportes'
        ]
      },
      {
        id: 'designPreference',
        label: 'Preferencia de Diseño',
        type: 'radio',
        options: [
          'Moderno y minimalista',
          'Corporativo y profesional',
          'Creativo y llamativo',
          'Clásico y elegante'
        ]
      }
    ]
  },
  security: {
    icon: Shield,
    color: 'from-red-600 to-orange-600',
    fields: [
      {
        id: 'securityType',
        label: 'Tipo de Análisis de Seguridad',
        type: 'select',
        required: true,
        options: [
          'Auditoría completa de seguridad',
          'Análisis de vulnerabilidades web',
          'Pentesting de aplicaciones',
          'Revisión de código fuente',
          'Análisis de infraestructura',
          'Cumplimiento normativo (GDPR, ISO)',
          'Consultoría en ciberseguridad',
          'Otro'
        ]
      },
      {
        id: 'currentSecurity',
        label: 'Medidas de Seguridad Actuales',
        type: 'checkbox',
        options: [
          'Firewall configurado',
          'Certificados SSL/TLS',
          'Copias de seguridad regulares',
          'Autenticación de dos factores',
          'Monitoreo de seguridad',
          'Políticas de contraseñas',
          'Formación en ciberseguridad',
          'Ninguna medida implementada'
        ]
      },
      {
        id: 'urgency',
        label: 'Nivel de Urgencia',
        type: 'radio',
        options: [
          'Crítico - Incidente de seguridad activo',
          'Alto - Vulnerabilidades detectadas',
          'Medio - Evaluación preventiva',
          'Bajo - Consulta informativa'
        ]
      }
    ]
  },
  ai: {
    icon: Brain,
    color: 'from-purple-600 to-pink-600',
    fields: [
      {
        id: 'aiApplication',
        label: 'Aplicación de IA Deseada',
        type: 'select',
        required: true,
        options: [
          'Chatbot inteligente para atención al cliente',
          'Automatización de procesos (RPA)',
          'Análisis predictivo de datos',
          'Reconocimiento de imágenes/documentos',
          'Procesamiento de lenguaje natural',
          'Sistema de recomendaciones',
          'Optimización de operaciones',
          'Análisis de sentimientos',
          'Otro'
        ]
      },
      {
        id: 'dataAvailable',
        label: 'Datos Disponibles',
        type: 'checkbox',
        options: [
          'Base de datos de clientes',
          'Historial de ventas',
          'Documentos y archivos',
          'Imágenes o multimedia',
          'Logs de sistema',
          'Datos de comportamiento web',
          'Encuestas y feedback',
          'No tengo datos estructurados'
        ]
      },
      {
        id: 'aiExperience',
        label: 'Experiencia con IA',
        type: 'radio',
        options: [
          'Ninguna - Primera implementación',
          'Básica - Herramientas simples',
          'Intermedia - Algunos proyectos',
          'Avanzada - Múltiples implementaciones'
        ]
      }
    ]
  },
  support: {
    icon: Wrench,
    color: 'from-green-600 to-teal-600',
    fields: [
      {
        id: 'supportType',
        label: 'Tipo de Asistencia Requerida',
        type: 'select',
        required: true,
        options: [
          'Soporte técnico puntual',
          'Mantenimiento web regular',
          'Resolución de problemas urgentes',
          'Optimización de rendimiento',
          'Actualización de sistemas',
          'Migración de datos/servidores',
          'Formación técnica',
          'Consultoría tecnológica',
          'Otro'
        ]
      },
      {
        id: 'currentIssues',
        label: 'Problemas Actuales',
        type: 'checkbox',
        options: [
          'Sitio web lento',
          'Errores frecuentes',
          'Problemas de seguridad',
          'Incompatibilidad móvil',
          'Caídas del servidor',
          'Problemas de email',
          'Actualizaciones pendientes',
          'Falta de copias de seguridad'
        ]
      },
      {
        id: 'supportFrequency',
        label: 'Frecuencia de Soporte Deseada',
        type: 'radio',
        options: [
          'Puntual - Solo este proyecto',
          'Mensual - Revisiones regulares',
          'Semanal - Soporte continuo',
          'Bajo demanda - Cuando sea necesario'
        ]
      }
    ]
  }
}

export default function ServiceContactForm({ serviceType, serviceName, serviceDescription }: ServiceContactFormProps) {
  const config = serviceConfigs[serviceType]
  const Icon = config.icon

  // Inicializar formData con campos específicos del servicio
  const initializeFormData = () => {
    const baseData = {
      name: '',
      email: '',
      phone: '',
      company: '',
      budget: '',
      timeline: '',
      message: '',
      service: serviceName,
    }

    // Añadir campos específicos del servicio
    const serviceFields = config.fields.reduce((acc, field) => {
      acc[field.id] = field.type === 'checkbox' ? [] : ''
      return acc
    }, {} as Record<string, string | string[]>)

    return { ...baseData, ...serviceFields }
  }

  const [formData, setFormData] = useState(initializeFormData())
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleInputChange = (field: string, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setSubmitStatus('idle')

    try {
      // Validar campos requeridos
      if (!formData.name || !formData.email) {
        console.error('Campos requeridos faltantes')
        setSubmitStatus('error')
        return
      }

      // Preparar datos específicos del servicio
      const serviceSpecificData = config.fields.reduce((acc, field) => {
        const value = formData[field.id as keyof typeof formData]
        if (value && value !== '') {
          if (Array.isArray(value)) {
            acc[field.id] = value.length > 0 ? value.join(', ') : ''
          } else {
            acc[field.id] = value as string
          }
        }
        return acc
      }, {} as Record<string, string>)

      // Crear mensaje detallado
      const serviceDetails = Object.entries(serviceSpecificData)
        .filter(([_, value]) => value && value !== '')
        .map(([key, value]) => {
          const field = config.fields.find(f => f.id === key)
          return `${field?.label}: ${value}`
        })
        .join('\n')

      const detailedMessage = [
        formData.message,
        serviceDetails ? `\n--- DETALLES ESPECÍFICOS DEL SERVICIO ---\n${serviceDetails}` : ''
      ].filter(Boolean).join('').trim()

      // Asegurar que el mensaje tenga contenido mínimo
      const finalMessage = detailedMessage.trim() || `Consulta sobre ${serviceName} - Información adicional proporcionada en campos específicos del servicio.`

      // Preparar datos para envío
      const submitData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone?.trim() || '',
        company: formData.company?.trim() || '',
        budget: formData.budget || '',
        timeline: formData.timeline || '',
        message: finalMessage,
        service: serviceName
      }

      console.log('Enviando datos:', submitData)

      // Función de prueba inline para aislar completamente el problema
      console.log('=== PRUEBA INLINE BÁSICA ===')
      try {
        const inlineTest = async () => {
          console.log('Función inline iniciada')
          return { success: true, message: 'Prueba inline exitosa' }
        }
        const inlineResult = await inlineTest()
        console.log('Resultado inline:', inlineResult)
      } catch (inlineError) {
        console.error('Error en prueba inline:', inlineError)
      }

      // Probar función sync (sin async/await)
      console.log('=== PROBANDO FUNCIÓN SYNC ===')
      try {
        const syncResult = submitContactFormSync(submitData)
        console.log('Resultado función sync:', syncResult)
      } catch (syncError) {
        console.error('Error en función sync:', syncError)
      }

      // Probar función con MCP de Supabase
      console.log('=== PROBANDO FUNCIÓN MCP ===')
      try {
        const mcpResult = await submitContactFormMCP(submitData)
        console.log('Resultado función MCP:', mcpResult)

        // Si MCP funciona, usar ese resultado
        if (mcpResult.success) {
          console.log('✅ MCP funcionó correctamente, usando ese resultado')
          if (mcpResult.success) {
            setSubmitStatus('success')
            setFormData(initializeFormData())
            return // Salir aquí si MCP funciona
          }
        }
      } catch (mcpError) {
        console.error('Error en función MCP:', mcpError)
      }

      // Usar función simple primero para debugging
      console.log('=== PROBANDO FUNCIÓN SIMPLE ===')
      try {
        const simpleResult = await submitContactFormSimple(submitData)
        console.log('Resultado función simple:', simpleResult)
      } catch (simpleError) {
        console.error('Error en función simple:', simpleError)
      }

      // Luego probar la función completa con manejo de errores extremo
      console.log('=== PROBANDO FUNCIÓN COMPLETA ===')
      let result
      try {
        result = await submitContactForm(submitData)
        console.log('Resultado función completa:', result)
      } catch (complexError) {
        console.error('Error capturado en componente:', complexError)
        console.error('Tipo:', typeof complexError)
        console.error('Constructor:', complexError?.constructor?.name)
        console.error('Es undefined?:', complexError === undefined)
        console.error('Es null?:', complexError === null)
        result = {
          success: false,
          message: 'Error capturado en el componente'
        }
      }

      if (result.success) {
        setSubmitStatus('success')
        // Reset form
        setFormData(initializeFormData())
      } else {
        console.error('Error en submitContactForm:', result.error)
        setSubmitStatus('error')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      setSubmitStatus('error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className={`p-3 rounded-xl bg-gradient-to-r ${config.color}`}>
              <Icon className="w-6 h-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl">Solicitar {serviceName}</CardTitle>
              <CardDescription className="text-lg">
                {serviceDescription}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Información de Contacto */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Nombre Completo *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                  placeholder="Tu nombre completo"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  required
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+34 ***********"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company">Empresa</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  placeholder="Nombre de tu empresa"
                />
              </div>
            </div>

            {/* Campos Específicos del Servicio */}
            <div className="space-y-6">
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-brand-primary" />
                  Detalles Específicos del Servicio
                </h3>
                
                {config.fields.map((field) => (
                  <div key={field.id} className="mb-6">
                    <Label className="text-base font-medium mb-3 block">
                      {field.label} {field.required && '*'}
                    </Label>
                    
                    {field.type === 'select' && (
                      <Select onValueChange={(value) => handleInputChange(field.id, value)}>
                        <SelectTrigger>
                          <SelectValue placeholder={`Selecciona ${field.label.toLowerCase()}`} />
                        </SelectTrigger>
                        <SelectContent>
                          {field.options?.map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}

                    {field.type === 'checkbox' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {field.options?.map((option) => (
                          <div key={option} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${field.id}-${option}`}
                              onCheckedChange={(checked) => {
                                const currentValues = (formData[field.id as keyof typeof formData] as string[]) || []
                                if (checked) {
                                  handleInputChange(field.id, [...currentValues, option])
                                } else {
                                  handleInputChange(field.id, currentValues.filter(v => v !== option))
                                }
                              }}
                            />
                            <Label htmlFor={`${field.id}-${option}`} className="text-sm">
                              {option}
                            </Label>
                          </div>
                        ))}
                      </div>
                    )}

                    {field.type === 'radio' && (
                      <RadioGroup onValueChange={(value) => handleInputChange(field.id, value)}>
                        {field.options?.map((option) => (
                          <div key={option} className="flex items-center space-x-2">
                            <RadioGroupItem value={option} id={`${field.id}-${option}`} />
                            <Label htmlFor={`${field.id}-${option}`}>
                              {option}
                            </Label>
                          </div>
                        ))}
                      </RadioGroup>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Presupuesto y Timeline */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="budget">Presupuesto Estimado</Label>
                <Select onValueChange={(value) => handleInputChange('budget', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona tu presupuesto" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="500-1000">€500 - €1,000</SelectItem>
                    <SelectItem value="1000-2500">€1,000 - €2,500</SelectItem>
                    <SelectItem value="2500-5000">€2,500 - €5,000</SelectItem>
                    <SelectItem value="5000-10000">€5,000 - €10,000</SelectItem>
                    <SelectItem value="10000+">€10,000+</SelectItem>
                    <SelectItem value="consultar">A consultar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="timeline">Timeline Deseado</Label>
                <Select onValueChange={(value) => handleInputChange('timeline', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="¿Cuándo necesitas el proyecto?" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="urgente">Urgente (1-2 semanas)</SelectItem>
                    <SelectItem value="1-mes">1 mes</SelectItem>
                    <SelectItem value="2-3-meses">2-3 meses</SelectItem>
                    <SelectItem value="3-6-meses">3-6 meses</SelectItem>
                    <SelectItem value="flexible">Flexible</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Mensaje Adicional */}
            <div className="space-y-2">
              <Label htmlFor="message">Información Adicional</Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                placeholder="Cuéntanos más detalles sobre tu proyecto, objetivos específicos, o cualquier pregunta que tengas..."
                rows={4}
              />
            </div>

            {/* Submit Button */}
            <div className="flex flex-col items-center space-y-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className={`w-full md:w-auto px-8 py-3 bg-gradient-to-r ${config.color} hover:opacity-90 transition-all duration-300`}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Enviando...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Solicitar {serviceName}
                  </>
                )}
              </Button>

              {/* Status Messages */}
              {submitStatus === 'success' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center space-x-2 text-green-600"
                >
                  <CheckCircle className="w-5 h-5" />
                  <span>¡Consulta enviada exitosamente! Te contactaremos pronto.</span>
                </motion.div>
              )}

              {submitStatus === 'error' && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center space-x-2 text-red-600"
                >
                  <AlertCircle className="w-5 h-5" />
                  <span>Error al enviar la consulta. Por favor, inténtalo de nuevo.</span>
                </motion.div>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}
