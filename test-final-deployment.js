// Script para probar el despliegue final
const PRODUCTION_URL = 'https://fastidious-centaur-160402.netlify.app';

async function testAuthentication() {
  console.log('🔐 Probando autenticación...');
  
  try {
    const response = await fetch(`${PRODUCTION_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    console.log(`Status: ${response.status}`);
    
    if (response.status === 200) {
      const data = await response.json();
      console.log('✅ Autenticación exitosa:', data);
      return true;
    } else {
      const errorData = await response.text();
      console.log('❌ Error en autenticación:', errorData);
      return false;
    }
  } catch (error) {
    console.log('❌ Error de conexión:', error.message);
    return false;
  }
}

async function testContactForm() {
  console.log('📝 Probando páginas con formularios de contacto...');

  try {
    // Test main page with contact form
    const response = await fetch(PRODUCTION_URL);

    console.log(`Status: ${response.status}`);

    if (response.status === 200) {
      const pageContent = await response.text();
      // Check if the page contains contact form elements
      if (pageContent.includes('<form') && (pageContent.includes('contacto') || pageContent.includes('Contacto') || pageContent.includes('email'))) {
        console.log('✅ Página principal con formulario de contacto carga correctamente');
        return true;
      } else {
        console.log('❌ La página no contiene formulario de contacto visible');
        // Let's also check for admin panel access
        const adminResponse = await fetch(`${PRODUCTION_URL}/admin`);
        if (adminResponse.status === 200) {
          console.log('✅ Panel de administración accesible');
          return true;
        }
        return false;
      }
    } else {
      console.log('❌ Error cargando página principal:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Error de conexión:', error.message);
    return false;
  }
}

async function testMainPage() {
  console.log('🏠 Probando página principal...');
  
  try {
    const response = await fetch(PRODUCTION_URL);
    
    if (response.status === 200) {
      console.log('✅ Página principal carga correctamente');
      return true;
    } else {
      console.log('❌ Error cargando página principal:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Error de conexión:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Iniciando pruebas del despliegue final...\n');
  
  const results = {
    mainPage: await testMainPage(),
    authentication: await testAuthentication(),
    contactForm: await testContactForm()
  };
  
  console.log('\n📊 Resumen de pruebas:');
  console.log(`Página principal: ${results.mainPage ? '✅' : '❌'}`);
  console.log(`Autenticación: ${results.authentication ? '✅' : '❌'}`);
  console.log(`Formulario de contacto: ${results.contactForm ? '✅' : '❌'}`);
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 ¡Todas las pruebas pasaron! El despliegue es exitoso.');
  } else {
    console.log('\n⚠️ Algunas pruebas fallaron. Revisar los errores arriba.');
  }
  
  return allPassed;
}

// Ejecutar las pruebas
runAllTests().catch(console.error);
