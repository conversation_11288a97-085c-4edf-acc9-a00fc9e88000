// Script para generar las claves API de Supabase para el proyecto karedesk
const jwt = require('jsonwebtoken')

// Información del proyecto karedesk
const PROJECT_REF = 'hswatweayaatytzzyrdg'
const PROJECT_URL = `https://${PROJECT_REF}.supabase.co`

// JWT Secret que necesitamos obtener del proyecto
// Por ahora vamos a usar un placeholder y luego lo actualizaremos
const JWT_SECRET = 'PLACEHOLDER_JWT_SECRET'

function generateSupabaseKeys() {
  console.log('🔑 Generando claves API para proyecto karedesk...')
  console.log('Project Ref:', PROJECT_REF)
  console.log('Project URL:', PROJECT_URL)
  
  try {
    // Generar clave anónima (anon key)
    const anonPayload = {
      iss: 'supabase',
      ref: PROJECT_REF,
      role: 'anon',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60) // 1 año
    }
    
    const anonKey = jwt.sign(anonPayload, JWT_SECRET)
    
    // Generar clave de servicio (service_role key)
    const servicePayload = {
      iss: 'supabase',
      ref: PROJECT_REF,
      role: 'service_role',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60) // 1 año
    }
    
    const serviceKey = jwt.sign(servicePayload, JWT_SECRET)
    
    console.log('\n✅ Claves generadas:')
    console.log('\n📋 Variables de entorno para Vercel:')
    console.log('NEXT_PUBLIC_SUPABASE_URL=' + PROJECT_URL)
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=' + anonKey)
    console.log('SUPABASE_SERVICE_ROLE_KEY=' + serviceKey)
    console.log('SUPABASE_URL=' + PROJECT_URL)
    console.log('SUPABASE_ANON_KEY=' + anonKey)
    
    return {
      url: PROJECT_URL,
      anonKey,
      serviceKey
    }
    
  } catch (error) {
    console.error('❌ Error generando claves:', error.message)
    return null
  }
}

// Función para probar las claves generadas
async function testGeneratedKeys(keys) {
  if (!keys) return
  
  console.log('\n🧪 Probando claves generadas...')
  
  try {
    // Probar conexión con clave anónima
    const response = await fetch(`${keys.url}/rest/v1/`, {
      headers: {
        'apikey': keys.anonKey,
        'Authorization': `Bearer ${keys.anonKey}`
      }
    })
    
    console.log('Status de prueba:', response.status)
    
    if (response.ok) {
      console.log('✅ Claves funcionan correctamente')
    } else {
      console.log('❌ Error con las claves:', response.statusText)
    }
    
  } catch (error) {
    console.error('❌ Error probando claves:', error.message)
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  console.log('⚠️  Necesitas el JWT_SECRET del proyecto karedesk para generar las claves')
  console.log('Puedes obtenerlo desde el dashboard de Supabase o desde las variables de entorno existentes')
}

module.exports = { generateSupabaseKeys, testGeneratedKeys }
