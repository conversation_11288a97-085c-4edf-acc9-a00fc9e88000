-- Insertar datos de ejemplo para testing (opcional)
INSERT INTO contacts (
    name, email, phone, company, service, message, budget, timeline, status, priority
) VALUES 
    ('<PERSON>', '<EMAIL>', '+1234567890', 'TechCorp', 'Asistencia Informática', 'Necesitamos soporte técnico para nuestros servidores', '5000-10000', '1-mes', 'new', 'high'),
    ('<PERSON>', '<EMAIL>', '+1234567891', 'InnovaCorp', 'Aná<PERSON>is de Vulnerabilidades', 'Queremos una auditoría de seguridad completa', '10000-25000', '2-3-meses', 'contacted', 'high'),
    ('<PERSON>', '<EMAIL>', '+1234567892', 'DigitalPro', 'Consultoría en Inteligencia Artificial', 'Implementar chatbot para atención al cliente', '25000+', 'flexible', 'in_progress', 'medium'),
    ('<PERSON>', '<EMAIL>', '+1234567893', 'StartupTech', 'Creación de Páginas Web', 'Necesitamos un e-commerce moderno', '1000-5000', 'inmediato', 'new', 'medium'),
    ('<PERSON> Silva', '<EMAIL>', '+1234567894', 'FinTech Pro', 'Análisis de Vulnerabilidades', 'Pentesting para aplicación financiera', '10000-25000', '1-2-semanas', 'quoted', 'high')
ON CONFLICT DO NOTHING;

-- Insertar actividades de ejemplo
INSERT INTO contact_activities (contact_id, activity_type, description, performed_by) VALUES 
    (1, 'email_sent', 'Email de confirmación enviado automáticamente', '<EMAIL>'),
    (2, 'email_sent', 'Email de confirmación enviado automáticamente', '<EMAIL>'),
    (2, 'call_made', 'Llamada inicial realizada, cliente interesado', '<EMAIL>'),
    (3, 'email_sent', 'Email de confirmación enviado automáticamente', '<EMAIL>'),
    (3, 'meeting_scheduled', 'Reunión programada para el viernes', '<EMAIL>'),
    (4, 'email_sent', 'Email de confirmación enviado automáticamente', '<EMAIL>'),
    (5, 'email_sent', 'Email de confirmación enviado automáticamente', '<EMAIL>'),
    (5, 'quote_sent', 'Cotización enviada por $15,000', '<EMAIL>')
ON CONFLICT DO NOTHING;
