import { useState, useCallback } from 'react'

export interface FormData {
  name: string
  email: string
  phone: string
  company: string
  message: string
  budget: string
  timeline: string
}

export interface FormErrors {
  [key: string]: string
}

export interface FormTouched {
  [key: string]: boolean
}

// Validación en tiempo real - Adaptado para España
const validateField = (name: string, value: string): string => {
  switch (name) {
    case 'name':
      return value.length < 2 ? 'El nombre debe tener al menos 2 caracteres' : ''
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return !emailRegex.test(value) ? 'Introduce un email válido' : ''
    case 'phone':
      // Teléfono opcional - solo validar si se proporciona
      if (value.trim() === '') return '' // Permitir vacío
      const phoneDigits = value.replace(/\D/g, '')
      // Validación para números españoles (9 dígitos)
      return phoneDigits.length < 9 ? 'El teléfono debe tener al menos 9 dígitos' : ''
    case 'message':
      return value.trim().length < 10 ? 'El mensaje debe tener al menos 10 caracteres' : ''
    default:
      return ''
  }
}

// Formatear teléfono automáticamente - Formato español
const formatPhone = (value: string): string => {
  const digits = value.replace(/\D/g, '')

  // Formato español: 123 456 789 o +34 123 456 789
  if (digits.length === 0) return ''
  if (digits.length <= 3) return digits
  if (digits.length <= 6) return `${digits.slice(0, 3)} ${digits.slice(3)}`
  if (digits.length <= 9) return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6)}`

  // Si empieza con 34 (código de España), formatear como +34 XXX XXX XXX
  if (digits.startsWith('34') && digits.length >= 11) {
    return `+34 ${digits.slice(2, 5)} ${digits.slice(5, 8)} ${digits.slice(8, 11)}`
  }

  // Formato estándar español (9 dígitos)
  return `${digits.slice(0, 3)} ${digits.slice(3, 6)} ${digits.slice(6, 9)}`
}

export const useContactForm = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    message: '',
    budget: '',
    timeline: ''
  })

  const [fieldErrors, setFieldErrors] = useState<FormErrors>({})
  const [touched, setTouched] = useState<FormTouched>({})

  // Manejar cambios en los campos
  const handleFieldChange = useCallback((name: string, value: string) => {
    let processedValue = value

    // Formatear teléfono automáticamente
    if (name === 'phone') {
      processedValue = formatPhone(value)
    }

    // Limitar longitud del mensaje
    if (name === 'message' && value.length > 500) {
      processedValue = value.slice(0, 500)
    }

    setFormData(prev => ({ ...prev, [name]: processedValue }))

    // Validar solo si el campo ha sido tocado
    if (touched[name]) {
      const error = validateField(name, processedValue)
      setFieldErrors(prev => ({ ...prev, [name]: error }))
    }
  }, [touched])

  // Manejar cuando un campo pierde el foco
  const handleFieldBlur = useCallback((name: string) => {
    setTouched(prev => ({ ...prev, [name]: true }))
    const error = validateField(name, formData[name])
    setFieldErrors(prev => ({ ...prev, [name]: error }))
  }, [formData])

  // Verificar si el formulario es válido - Teléfono opcional
  const isFormValid = useCallback(() => {
    const requiredFields = ['name', 'email', 'message'] // Teléfono ya no es obligatorio
    const requiredFieldsValid = requiredFields.every(field =>
      formData[field].trim() !== '' && !fieldErrors[field]
    )

    // Verificar que no haya errores en campos opcionales (como teléfono)
    const noFieldErrors = Object.keys(fieldErrors).every(field => !fieldErrors[field])

    return requiredFieldsValid && noFieldErrors
  }, [formData, fieldErrors])

  // Resetear formulario
  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      message: '',
      budget: '',
      timeline: ''
    })
    setFieldErrors({})
    setTouched({})
  }, [])

  // Validar todo el formulario
  const validateAllFields = useCallback(() => {
    const newErrors: FormErrors = {}
    const newTouched: FormTouched = {}

    Object.keys(formData).forEach(field => {
      newTouched[field] = true
      const error = validateField(field, formData[field as keyof FormData])
      if (error) {
        newErrors[field] = error
      }
    })

    setTouched(newTouched)
    setFieldErrors(newErrors)

    return Object.keys(newErrors).length === 0
  }, [formData])

  return {
    formData,
    fieldErrors,
    touched,
    handleFieldChange,
    handleFieldBlur,
    isFormValid,
    resetForm,
    validateAllFields
  }
}
