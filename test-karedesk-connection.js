// Script para probar la conexión con el proyecto karedesk correcto
const { createClient } = require('@supabase/supabase-js')

// Configuración del proyecto karedesk correcto
const KAREDESK_URL = 'https://hswatweayaatytzzyrdg.supabase.co'

// Necesitamos obtener las claves API correctas
// Por ahora vamos a probar con las claves que tenemos

async function testKaredeskConnection() {
  console.log('🔍 Probando conexión con proyecto karedesk...')
  console.log('URL:', KAREDESK_URL)
  
  try {
    // Vamos a probar primero con una consulta simple
    const response = await fetch(`${KAREDESK_URL}/rest/v1/`, {
      headers: {
        'apikey': 'PLACEHOLDER_ANON_KEY',
        'Authorization': 'Bearer PLACEHOLDER_ANON_KEY'
      }
    })
    
    console.log('Status:', response.status)
    console.log('Headers:', Object.fromEntries(response.headers.entries()))
    
    if (response.ok) {
      console.log('✅ Conexión exitosa con karedesk')
    } else {
      console.log('❌ Error de conexión:', response.statusText)
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

// Función para probar login con admin
async function testAdminLogin() {
  console.log('\n🔐 Probando login de admin...')
  
  try {
    const response = await fetch(`${KAREDESK_URL}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'PLACEHOLDER_ANON_KEY'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    })
    
    console.log('Login Status:', response.status)
    
    if (response.ok) {
      const data = await response.json()
      console.log('✅ Login exitoso!')
      console.log('User ID:', data.user?.id)
      console.log('Email:', data.user?.email)
    } else {
      const error = await response.text()
      console.log('❌ Error de login:', error)
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

// Ejecutar tests
testKaredeskConnection()
testAdminLogin()
