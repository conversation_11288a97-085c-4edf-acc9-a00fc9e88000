// Script para probar la autenticación una vez que se corrijan las variables de entorno
const { createClient } = require('@supabase/supabase-js')

// Configuración del proyecto karedesk correcto
const KAREDESK_URL = 'https://hswatweayaatytzzyrdg.supabase.co'

// Estas claves las obtendremos del dashboard de Supabase
const KAREDESK_ANON_KEY = 'PLACEHOLDER_ANON_KEY'
const KAREDESK_SERVICE_KEY = 'PLACEHOLDER_SERVICE_KEY'

async function testKaredeskAuth() {
  console.log('🔐 Probando autenticación con proyecto karedesk...')
  console.log('URL:', KAREDESK_URL)
  
  try {
    // Crear cliente de Supabase con las credenciales correctas
    const supabase = createClient(KAREDESK_URL, KAREDESK_ANON_KEY)
    
    console.log('✅ Cliente de Supabase creado')
    
    // Probar login con admin
    console.log('🔑 Intentando login con admin...')
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'admin123'
    })
    
    if (error) {
      console.error('❌ Error de login:', error.message)
      return false
    }
    
    if (data.user) {
      console.log('✅ Login exitoso!')
      console.log('User ID:', data.user.id)
      console.log('Email:', data.user.email)
      console.log('Email confirmado:', data.user.email_confirmed_at)
      return true
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    return false
  }
}

async function testContactFormSubmission() {
  console.log('\n📝 Probando envío de formulario de contacto...')
  
  try {
    // Crear cliente con service key para insertar datos
    const supabaseAdmin = createClient(KAREDESK_URL, KAREDESK_SERVICE_KEY)
    
    // Datos de prueba
    const testContact = {
      name: 'Test Usuario',
      email: '<EMAIL>',
      phone: '+34 ***********',
      company: 'Test Company',
      service: 'web-creation',
      message: 'Mensaje de prueba desde script de verificación',
      budget: '1000-5000',
      timeline: '1-3-months',
      priority: 'normal'
    }
    
    console.log('📊 Datos de prueba:', testContact)
    
    // Insertar contacto
    const { data, error } = await supabaseAdmin
      .from('contacts')
      .insert([testContact])
      .select()
    
    if (error) {
      console.error('❌ Error insertando contacto:', error.message)
      return false
    }
    
    console.log('✅ Contacto insertado exitosamente!')
    console.log('ID:', data[0]?.id)
    console.log('Creado:', data[0]?.created_at)
    
    return true
    
  } catch (error) {
    console.error('❌ Error:', error.message)
    return false
  }
}

async function runAllTests() {
  console.log('🚀 Iniciando pruebas de autenticación y funcionalidad...\n')
  
  const authResult = await testKaredeskAuth()
  const formResult = await testContactFormSubmission()
  
  console.log('\n📊 Resumen de pruebas:')
  console.log('Autenticación:', authResult ? '✅ EXITOSA' : '❌ FALLIDA')
  console.log('Formulario de contacto:', formResult ? '✅ EXITOSO' : '❌ FALLIDO')
  
  if (authResult && formResult) {
    console.log('\n🎉 ¡Todas las pruebas pasaron! El sistema está funcionando correctamente.')
  } else {
    console.log('\n⚠️  Algunas pruebas fallaron. Revisar configuración.')
  }
}

// Función para actualizar las claves en este script
function updateKeys(anonKey, serviceKey) {
  console.log('🔄 Actualizando claves API...')
  console.log('Anon Key:', anonKey.substring(0, 20) + '...')
  console.log('Service Key:', serviceKey.substring(0, 20) + '...')
  
  // Aquí actualizarías las constantes KAREDESK_ANON_KEY y KAREDESK_SERVICE_KEY
  // y luego ejecutarías las pruebas
}

// Exportar funciones para uso externo
module.exports = {
  testKaredeskAuth,
  testContactFormSubmission,
  runAllTests,
  updateKeys
}

// Ejecutar si se llama directamente
if (require.main === module) {
  console.log('⚠️  Necesitas actualizar las claves API antes de ejecutar las pruebas')
  console.log('Obtén las claves del dashboard de Supabase y actualiza este script')
}
