import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Solo proteger rutas admin
  if (request.nextUrl.pathname.startsWith('/admin')) {
    // Verificar si hay token de sesión en las cookies
    const accessToken = request.cookies.get('sb-access-token')

    if (!accessToken?.value) {
      // No hay token, redirigir a login
      const url = request.nextUrl.clone()
      url.pathname = '/login'
      url.searchParams.set('redirectTo', request.nextUrl.pathname)
      return NextResponse.redirect(url)
    }

    // Token presente, permitir acceso
    // La validación completa se hace en el AdminLayoutClient
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Solo aplicar middleware a rutas admin
     */
    '/admin/:path*',
  ],
}
