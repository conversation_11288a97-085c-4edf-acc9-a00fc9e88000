#!/usr/bin/env node

/**
 * Test manual para verificar que el admin dashboard funciona correctamente con Supabase
 */

const https = require('https');

// Configuración
const BASE_URL = 'https://karedesk.com';
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

// Función para hacer requests HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: body ? JSON.parse(body) : null
          };
          resolve(result);
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body,
            data: null
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Test 1: Login
async function testLogin() {
  console.log('🔐 Probando login...');
  
  const options = {
    hostname: 'karedesk.com',
    port: 443,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await makeRequest(options, ADMIN_CREDENTIALS);
    
    if (response.statusCode === 200 && response.data?.success) {
      console.log('✅ Login exitoso');
      
      // Extraer cookies
      const setCookieHeaders = response.headers['set-cookie'] || [];
      const cookies = {};
      
      setCookieHeaders.forEach(cookie => {
        const [nameValue] = cookie.split(';');
        const [name, value] = nameValue.split('=');
        cookies[name] = value;
      });
      
      return cookies;
    } else {
      console.log('❌ Login falló:', response.data);
      return null;
    }
  } catch (error) {
    console.log('❌ Error en login:', error.message);
    return null;
  }
}

// Test 2: Verificar autenticación
async function testAuthVerification(cookies) {
  console.log('🔍 Verificando autenticación...');
  
  const cookieString = Object.entries(cookies)
    .map(([name, value]) => `${name}=${value}`)
    .join('; ');

  const options = {
    hostname: 'karedesk.com',
    port: 443,
    path: '/api/auth/verify',
    method: 'GET',
    headers: {
      'Cookie': cookieString
    }
  };

  try {
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data?.authenticated) {
      console.log('✅ Autenticación verificada');
      return true;
    } else {
      console.log('❌ Verificación de autenticación falló:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Error en verificación:', error.message);
    return false;
  }
}

// Test 3: Obtener estadísticas
async function testStats(cookies) {
  console.log('📊 Probando API de estadísticas...');
  
  const cookieString = Object.entries(cookies)
    .map(([name, value]) => `${name}=${value}`)
    .join('; ');

  const options = {
    hostname: 'karedesk.com',
    port: 443,
    path: '/api/admin/stats',
    method: 'GET',
    headers: {
      'Cookie': cookieString
    }
  };

  try {
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data?.success) {
      console.log('✅ Estadísticas obtenidas exitosamente');
      console.log(`   - Total contactos: ${response.data.data.total}`);
      console.log(`   - Este mes: ${response.data.data.thisMonth}`);
      console.log(`   - Esta semana: ${response.data.data.thisWeek}`);
      console.log(`   - Por servicio:`, response.data.data.byService);
      return true;
    } else {
      console.log('❌ Error obteniendo estadísticas:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Error en estadísticas:', error.message);
    return false;
  }
}

// Test 4: Obtener contactos
async function testContacts(cookies) {
  console.log('👥 Probando API de contactos...');
  
  const cookieString = Object.entries(cookies)
    .map(([name, value]) => `${name}=${value}`)
    .join('; ');

  const options = {
    hostname: 'karedesk.com',
    port: 443,
    path: '/api/admin/contacts?limit=3',
    method: 'GET',
    headers: {
      'Cookie': cookieString
    }
  };

  try {
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data?.success) {
      console.log('✅ Contactos obtenidos exitosamente');
      console.log(`   - Total en respuesta: ${response.data.data.length}`);
      console.log(`   - Primer contacto: ${response.data.data[0]?.name} (${response.data.data[0]?.email})`);
      return true;
    } else {
      console.log('❌ Error obteniendo contactos:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ Error en contactos:', error.message);
    return false;
  }
}

// Test 5: Verificar conexión a Supabase
async function testSupabaseConnection() {
  console.log('🗄️  Verificando conexión directa a Supabase...');
  
  const options = {
    hostname: 'karedesk.com',
    port: 443,
    path: '/api/test-supabase',
    method: 'GET'
  };

  try {
    const response = await makeRequest(options);
    
    if (response.statusCode === 200 && response.data?.success) {
      console.log('✅ Conexión a Supabase verificada');
      return true;
    } else {
      console.log('⚠️  Test de Supabase no disponible o falló');
      return false;
    }
  } catch (error) {
    console.log('⚠️  Error verificando Supabase:', error.message);
    return false;
  }
}

// Ejecutar todos los tests
async function runAllTests() {
  console.log('🚀 Iniciando tests del Admin Dashboard\n');
  
  const results = {
    login: false,
    auth: false,
    stats: false,
    contacts: false,
    supabase: false
  };

  // Test 1: Login
  const cookies = await testLogin();
  if (cookies) {
    results.login = true;
    
    // Test 2: Verificar autenticación
    results.auth = await testAuthVerification(cookies);
    
    // Test 3: Estadísticas
    results.stats = await testStats(cookies);
    
    // Test 4: Contactos
    results.contacts = await testContacts(cookies);
  }
  
  // Test 5: Supabase (independiente)
  results.supabase = await testSupabaseConnection();
  
  // Resumen
  console.log('\n📋 RESUMEN DE TESTS:');
  console.log('===================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test.toUpperCase()}: ${passed ? 'PASÓ' : 'FALLÓ'}`);
  });
  
  const totalPassed = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 RESULTADO: ${totalPassed}/${totalTests} tests pasaron`);
  
  if (totalPassed === totalTests) {
    console.log('🎉 ¡Todos los tests pasaron! El admin dashboard está funcionando correctamente.');
  } else if (totalPassed >= 4) {
    console.log('✅ El admin dashboard está funcionando correctamente (tests críticos pasaron).');
  } else {
    console.log('⚠️  Hay problemas con el admin dashboard que necesitan atención.');
  }
}

// Ejecutar
runAllTests().catch(console.error);
