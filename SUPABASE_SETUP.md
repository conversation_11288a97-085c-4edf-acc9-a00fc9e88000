\n# Configuración de Supabase para Karedesk\n\nEste documento explica cómo configurar completamente Supabase para el proyecto Karedesk.\n\n## 1. Crear Proyecto en Supabase\n\n1. Ve a [https://supabase.com](https://supabase.com)\n2. Crea una cuenta o inicia sesión\n3. Crea un nuevo proyecto\n4. Anota la URL del proyecto y las claves de API\n\n## 2. Configurar Variables de Entorno\n\nCopia `.env.example` a `.env.local` y configura las variables:\n\n```bash\ncp .env.example .env.local\n```\n\nEdita `.env.local` con tus credenciales de Supabase:\n\n```env\n# Supabase Configuration\nNEXT_PUBLIC_SUPABASE_URL=https://tu-proyecto.supabase.co\nNEXT_PUBLIC_SUPABASE_ANON_KEY=tu_anon_key\nSUPABASE_SERVICE_ROLE_KEY=tu_service_role_key\n\n# Email Configuration (Resend)\nRESEND_API_KEY=tu_resend_api_key\n```\n\n## 3. Ejecutar Script SQL en Supabase\n\n1. Ve a tu proyecto en Supabase\n2. Navega a **SQL Editor** en el panel lateral\n3. Crea una nueva consulta\n4. Copia y pega el contenido de `scripts/supabase-setup.sql`\n5. Ejecuta la consulta\n\nEl script creará:\n- Tabla `contacts` con todos los campos necesarios\n- Índices para mejorar rendimiento\n- Trigger para actualizar `updated_at` automáticamente\n- Políticas de Row Level Security\n- Datos de prueba\n\n## 4. Verificar Configuración\n\nEjecuta los siguientes comandos para verificar que todo funciona:\n\n```bash\n# Verificar conexión\ncurl http://localhost:3000/api/test-supabase\n\n# Probar inserción de contacto\ncurl -X POST http://localhost:3000/api/test-supabase\n\n# Verificar admin panel\ncurl http://localhost:3000/api/admin/contacts\ncurl http://localhost:3000/api/admin/stats\n```\n\n## 5. Estructura de la Tabla Contacts\n\n```sql\nCREATE TABLE contacts (\n  id BIGSERIAL PRIMARY KEY,\n  name TEXT NOT NULL,\n  email TEXT NOT NULL,\n  phone TEXT,\n  company TEXT,\n  service TEXT NOT NULL,\n  message TEXT NOT NULL,\n  budget TEXT,\n  timeline TEXT,\n  priority TEXT DEFAULT 'normal',\n  client_email_id TEXT,\n  internal_email_id TEXT,\n  created_at TIMESTAMPTZ DEFAULT NOW(),\n  updated_at TIMESTAMPTZ DEFAULT NOW()\n);\n```\n\n## 6. Funcionalidades Implementadas\n\n- ✅ Guardar nuevos contactos desde formulario\n- ✅ Listar contactos en admin panel\n- ✅ Actualizar contactos individualmente\n- ✅ Obtener estadísticas básicas\n- ✅ Verificar conexión a base de datos\n- ✅ Prevención de contactos duplicados\n- ⏳ Sistema de actividades (pendiente)\n- ⏳ Filtros avanzados en admin (pendiente)\n\n## 7. Migración desde Neon\n\nSi tienes datos en Neon que quieres migrar:\n\n1. Exporta los datos de Neon\n2. Adapta el formato si es necesario\n3. Importa a Supabase usando el SQL Editor\n\n## 8. Notas de Seguridad\n\n- Row Level Security está habilitado\n- Solo el service role key puede acceder a los datos\n- Las claves anon solo pueden leer datos según las políticas\n- Revisa y ajusta las políticas según tus necesidades específicas\n\n## 9. Troubleshooting\n\n### Error: \"tabla contacts no existe\"\n- Ejecuta el script SQL en Supabase\n- Verifica que estés conectado al proyecto correcto\n\n### Error: \"no tienes permisos\"\n- Verifica que las variables de entorno estén correctas\n- Asegúrate de usar el service role key para operaciones admin\n\n### Error: \"conexión fallida\"\n- Verifica la URL del proyecto\n- Comprueba que las claves de API sean correctas\n- Asegúrate de que el proyecto esté activo en Supabase\n