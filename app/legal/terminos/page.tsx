import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Términos de Servicio",
  description: "Términos y condiciones de uso de los servicios de Karedesk",
}

export default function TerminosPage() {
  return (
    <div className="min-h-screen bg-hero-gradient py-32 px-4">
      <div className="container mx-auto max-w-4xl">
        <div className="bg-slate-900/50 backdrop-blur-xl border border-slate-700/30 rounded-3xl p-8 md:p-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 tracking-tight">
            Términos de Servicio
          </h1>
          
          <div className="prose prose-invert max-w-none">
            <p className="text-slate-300 text-lg mb-6">
              Estos términos rigen el uso de los servicios proporcionados por Karedesk.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">1. Aceptación de Términos</h2>
            <p className="text-slate-300 mb-6">
              Al utilizar nuestros servicios, aceptas estos términos y condiciones en su totalidad.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">2. Servicios Ofrecidos</h2>
            <p className="text-slate-300 mb-4">
              Karedesk ofrece servicios de:
            </p>
            <ul className="text-slate-300 mb-6 list-disc list-inside space-y-2">
              <li>Asistencia informática y soporte técnico</li>
              <li>Análisis de vulnerabilidades y seguridad web</li>
              <li>Consultoría en inteligencia artificial</li>
              <li>Desarrollo y creación de páginas web</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">3. Responsabilidades del Cliente</h2>
            <p className="text-slate-300 mb-4">
              El cliente se compromete a:
            </p>
            <ul className="text-slate-300 mb-6 list-disc list-inside space-y-2">
              <li>Proporcionar información veraz y actualizada</li>
              <li>Colaborar en el desarrollo del proyecto</li>
              <li>Realizar los pagos según lo acordado</li>
              <li>Respetar los derechos de propiedad intelectual</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">4. Limitación de Responsabilidad</h2>
            <p className="text-slate-300 mb-6">
              Karedesk no será responsable de daños indirectos, incidentales o consecuentes 
              que puedan surgir del uso de nuestros servicios.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">5. Propiedad Intelectual</h2>
            <p className="text-slate-300 mb-6">
              Los derechos de propiedad intelectual del trabajo desarrollado se establecerán 
              específicamente en cada contrato de servicio.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">6. Modificaciones</h2>
            <p className="text-slate-300 mb-6">
              Nos reservamos el derecho de modificar estos términos en cualquier momento. 
              Las modificaciones entrarán en vigor tras su publicación.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">7. Contacto</h2>
            <p className="text-slate-300 mb-6">
              Para consultas sobre estos términos:
              <br />
              <strong>Email:</strong> <EMAIL>
              <br />
              <strong>Teléfono:</strong> +34 123 456 789
            </p>

            <p className="text-slate-400 text-sm mt-8">
              Última actualización: Enero 2024
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
