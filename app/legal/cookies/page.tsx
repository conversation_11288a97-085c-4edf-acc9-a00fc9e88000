import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Política de Cookies",
  description: "Información sobre el uso de cookies en el sitio web de Karedesk",
}

export default function CookiesPage() {
  return (
    <div className="min-h-screen bg-hero-gradient py-32 px-4">
      <div className="container mx-auto max-w-4xl">
        <div className="bg-slate-900/50 backdrop-blur-xl border border-slate-700/30 rounded-3xl p-8 md:p-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-8 tracking-tight">
            Política de Cookies
          </h1>
          
          <div className="prose prose-invert max-w-none">
            <p className="text-slate-300 text-lg mb-6">
              Esta política explica cómo utilizamos las cookies en nuestro sitio web.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">¿Qué son las Cookies?</h2>
            <p className="text-slate-300 mb-6">
              Las cookies son pequeños archivos de texto que se almacenan en tu dispositivo 
              cuando visitas un sitio web. Nos ayudan a mejorar tu experiencia de navegación.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">Tipos de Cookies que Utilizamos</h2>
            
            <h3 className="text-xl font-semibold text-white mt-6 mb-3">Cookies Esenciales</h3>
            <p className="text-slate-300 mb-4">
              Necesarias para el funcionamiento básico del sitio web:
            </p>
            <ul className="text-slate-300 mb-6 list-disc list-inside space-y-2">
              <li>Cookies de sesión</li>
              <li>Cookies de seguridad</li>
              <li>Cookies de preferencias de idioma</li>
            </ul>

            <h3 className="text-xl font-semibold text-white mt-6 mb-3">Cookies de Rendimiento</h3>
            <p className="text-slate-300 mb-4">
              Nos ayudan a entender cómo interactúas con nuestro sitio:
            </p>
            <ul className="text-slate-300 mb-6 list-disc list-inside space-y-2">
              <li>Google Analytics</li>
              <li>Métricas de rendimiento</li>
              <li>Análisis de comportamiento</li>
            </ul>

            <h3 className="text-xl font-semibold text-white mt-6 mb-3">Cookies de Funcionalidad</h3>
            <p className="text-slate-300 mb-6">
              Mejoran la funcionalidad y personalización del sitio web.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">Control de Cookies</h2>
            <p className="text-slate-300 mb-4">
              Puedes controlar las cookies de las siguientes maneras:
            </p>
            <ul className="text-slate-300 mb-6 list-disc list-inside space-y-2">
              <li>Configuración del navegador</li>
              <li>Herramientas de opt-out</li>
              <li>Configuración de privacidad del sitio</li>
            </ul>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">Cookies de Terceros</h2>
            <p className="text-slate-300 mb-6">
              Algunos servicios de terceros pueden establecer cookies en nuestro sitio, 
              como Google Analytics para análisis de tráfico.
            </p>

            <h2 className="text-2xl font-bold text-white mt-8 mb-4">Contacto</h2>
            <p className="text-slate-300 mb-6">
              Para consultas sobre cookies:
              <br />
              <strong>Email:</strong> <EMAIL>
              <br />
              <strong>Teléfono:</strong> +34 123 456 789
            </p>

            <p className="text-slate-400 text-sm mt-8">
              Última actualización: Enero 2024
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
