"use client"

import { motion } from 'framer-motion'
import DashboardClient from '@/components/admin/dashboard-client'

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

export default function AdminDashboard() {
  return (
    <motion.div
      initial="initial"
      animate="animate"
      variants={fadeInUp}
    >
      <DashboardClient />
    </motion.div>
  )
}
