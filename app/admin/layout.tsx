import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import AdminLayoutClient from '@/components/admin/admin-layout-client'

export const metadata: Metadata = {
  title: "Admin Dashboard - Karedesk",
  description: "Panel de administración para gestionar consultas de clientes",
  robots: "noindex, nofollow", // Evitar indexación del admin
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <AdminLayoutClient>{children}</AdminLayoutClient>
}
