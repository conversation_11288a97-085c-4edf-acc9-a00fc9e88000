import { notFound } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Mail, Phone, Building, Calendar, MessageSquare, Euro, Clock } from 'lucide-react'
import Link from 'next/link'
import { getContactById } from '@/lib/supabase-database'

interface ContactDetailPageProps {
  params: Promise<{ id: string }>
}

export default async function ContactDetailPage({ params }: ContactDetailPageProps) {
  const { id } = await params
  const contactId = parseInt(id)

  if (isNaN(contactId)) {
    notFound()
  }

  try {
    const contact = await getContactById(contactId)
    
    if (!contact) {
      notFound()
    }

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString('es-ES', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    const getPriorityColor = (priority: string) => {
      switch (priority) {
        case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
        case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
        default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
      }
    }

    return (
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/contacts">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver a Contactos
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
              Detalle del Contacto
            </h1>
            <p className="text-slate-600 dark:text-slate-400">
              ID: {contact.id} • Recibido el {formatDate(contact.created_at)}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Información Principal */}
          <div className="lg:col-span-2 space-y-6">
            {/* Datos del Contacto */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="w-5 h-5" />
                  Información del Contacto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                      Nombre Completo
                    </label>
                    <p className="text-lg font-semibold text-slate-900 dark:text-white">
                      {contact.name}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                      Email
                    </label>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-slate-500" />
                      <a 
                        href={`mailto:${contact.email}`}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                      >
                        {contact.email}
                      </a>
                    </div>
                  </div>
                  {contact.phone && (
                    <div>
                      <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                        Teléfono
                      </label>
                      <div className="flex items-center gap-2">
                        <Phone className="w-4 h-4 text-slate-500" />
                        <a 
                          href={`tel:${contact.phone}`}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400"
                        >
                          {contact.phone}
                        </a>
                      </div>
                    </div>
                  )}
                  {contact.company && (
                    <div>
                      <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                        Empresa
                      </label>
                      <div className="flex items-center gap-2">
                        <Building className="w-4 h-4 text-slate-500" />
                        <p className="text-slate-900 dark:text-white">
                          {contact.company}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Mensaje */}
            <Card>
              <CardHeader>
                <CardTitle>Mensaje</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4">
                  <p className="text-slate-900 dark:text-white whitespace-pre-wrap">
                    {contact.message}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Detalles del Proyecto */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Detalles del Proyecto</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    Servicio Solicitado
                  </label>
                  <Badge variant="outline" className="mt-1">
                    {contact.service}
                  </Badge>
                </div>
                
                {contact.budget && (
                  <div>
                    <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                      Presupuesto
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <Euro className="w-4 h-4 text-slate-500" />
                      <span className="text-slate-900 dark:text-white">
                        {contact.budget}
                      </span>
                    </div>
                  </div>
                )}

                {contact.timeline && (
                  <div>
                    <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                      Timeline
                    </label>
                    <div className="flex items-center gap-2 mt-1">
                      <Clock className="w-4 h-4 text-slate-500" />
                      <span className="text-slate-900 dark:text-white">
                        {contact.timeline}
                      </span>
                    </div>
                  </div>
                )}

                <div>
                  <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    Prioridad
                  </label>
                  <Badge className={`mt-1 ${getPriorityColor(contact.priority || 'normal')}`}>
                    {contact.priority || 'Normal'}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Fechas */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Información de Seguimiento</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    Fecha de Recepción
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="w-4 h-4 text-slate-500" />
                    <span className="text-sm text-slate-900 dark:text-white">
                      {formatDate(contact.created_at)}
                    </span>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-slate-600 dark:text-slate-400">
                    Última Actualización
                  </label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="w-4 h-4 text-slate-500" />
                    <span className="text-sm text-slate-900 dark:text-white">
                      {formatDate(contact.updated_at)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Acciones */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Acciones</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" asChild>
                  <a href={`mailto:${contact.email}?subject=Re: ${contact.service}&body=Hola ${contact.name},%0D%0A%0D%0AGracias por contactarnos...`}>
                    <Mail className="w-4 h-4 mr-2" />
                    Responder por Email
                  </a>
                </Button>
                
                {contact.phone && (
                  <Button variant="outline" className="w-full" asChild>
                    <a href={`tel:${contact.phone}`}>
                      <Phone className="w-4 h-4 mr-2" />
                      Llamar
                    </a>
                  </Button>
                )}
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/admin/contacts/${contact.id}/edit`}>
                    Editar Contacto
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error fetching contact:', error)
    notFound()
  }
}
