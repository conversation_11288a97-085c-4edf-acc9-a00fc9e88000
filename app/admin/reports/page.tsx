import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  TrendingUp, 
  Download, 
  Calendar, 
  BarChart3,
  PieChart,
  Users,
  Mail,
  Euro,
  Clock,
  Target,
  Activity
} from 'lucide-react'

// Datos de ejemplo para reportes (en una implementación real vendrían de Supabase)
const monthlyStats = [
  { month: 'Enero', contacts: 45, conversions: 12, revenue: 15600 },
  { month: 'Febrero', contacts: 52, conversions: 18, revenue: 23400 },
  { month: 'Marzo', contacts: 38, conversions: 15, revenue: 19200 },
  { month: 'Abril', contacts: 61, conversions: 22, revenue: 28800 },
  { month: 'Mayo', contacts: 48, conversions: 16, revenue: 20400 },
  { month: 'Junio', contacts: 34, conversions: 11, revenue: 14200 }
]

const serviceStats = [
  { service: 'Desarrollo Web', count: 89, percentage: 35, revenue: 45600 },
  { service: 'Consultoría IT', count: 67, percentage: 26, revenue: 38200 },
  { service: 'Seguridad Web', count: 45, percentage: 18, revenue: 28900 },
  { service: 'Soporte Técnico', count: 34, percentage: 13, revenue: 15800 },
  { service: 'Otros', count: 20, percentage: 8, revenue: 9500 }
]

const conversionFunnel = [
  { stage: 'Visitantes Web', count: 2450, percentage: 100 },
  { stage: 'Formularios Iniciados', count: 485, percentage: 20 },
  { stage: 'Formularios Completados', count: 278, percentage: 11 },
  { stage: 'Consultas Calificadas', count: 156, percentage: 6 },
  { stage: 'Propuestas Enviadas', count: 94, percentage: 4 },
  { stage: 'Clientes Convertidos', count: 47, percentage: 2 }
]

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}

export default function ReportsPage() {
  const totalContacts = monthlyStats.reduce((sum, month) => sum + month.contacts, 0)
  const totalRevenue = monthlyStats.reduce((sum, month) => sum + month.revenue, 0)
  const totalConversions = monthlyStats.reduce((sum, month) => sum + month.conversions, 0)
  const conversionRate = totalContacts > 0 ? ((totalConversions / totalContacts) * 100).toFixed(1) : '0'

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Reportes y Analytics
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            Análisis detallado del rendimiento de Karedesk España
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            Filtrar Período
          </Button>
          <Button>
            <Download className="w-4 h-4 mr-2" />
            Exportar Reporte
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Total Contactos
            </CardTitle>
            <Users className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {totalContacts}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Últimos 6 meses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Tasa de Conversión
            </CardTitle>
            <Target className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {conversionRate}%
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Consultas a clientes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Ingresos Totales
            </CardTitle>
            <Euro className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {formatCurrency(totalRevenue)}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Últimos 6 meses
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Tiempo Promedio
            </CardTitle>
            <Clock className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              2.4h
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Respuesta a consultas
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Monthly Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="w-5 h-5" />
              Rendimiento Mensual
            </CardTitle>
            <CardDescription>
              Evolución de contactos y conversiones por mes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {monthlyStats.map((month, index) => {
                const conversionRate = month.contacts > 0 ? ((month.conversions / month.contacts) * 100).toFixed(1) : '0'
                return (
                  <div key={index} className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-slate-900 dark:text-white">
                          {month.month}
                        </h3>
                        <Badge variant="outline">
                          {conversionRate}% conversión
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400 mt-1">
                        <span>{month.contacts} contactos</span>
                        <span>{month.conversions} conversiones</span>
                        <span>{formatCurrency(month.revenue)}</span>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Services Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Servicios Más Solicitados
            </CardTitle>
            <CardDescription>
              Distribución de consultas por tipo de servicio
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {serviceStats.map((service, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-slate-900 dark:text-white">
                        {service.service}
                      </span>
                      <span className="text-sm text-slate-600 dark:text-slate-400">
                        {service.count} ({service.percentage}%)
                      </span>
                    </div>
                    <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                        style={{ width: `${service.percentage}%` }}
                      />
                    </div>
                    <div className="text-xs text-slate-500 dark:text-slate-500 mt-1">
                      Ingresos: {formatCurrency(service.revenue)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Conversion Funnel */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            Embudo de Conversión
          </CardTitle>
          <CardDescription>
            Análisis del proceso de conversión de visitantes a clientes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {conversionFunnel.map((stage, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className="w-24 text-sm font-medium text-slate-700 dark:text-slate-300">
                  {stage.percentage}%
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-slate-900 dark:text-white">
                      {stage.stage}
                    </span>
                    <span className="text-sm text-slate-600 dark:text-slate-400">
                      {stage.count.toLocaleString('es-ES')}
                    </span>
                  </div>
                  <div className="w-full bg-slate-200 dark:bg-slate-600 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full transition-all duration-300 ${
                        index === 0 ? 'bg-blue-600' :
                        index === 1 ? 'bg-green-600' :
                        index === 2 ? 'bg-yellow-600' :
                        index === 3 ? 'bg-orange-600' :
                        index === 4 ? 'bg-red-600' :
                        'bg-purple-600'
                      }`}
                      style={{ width: `${stage.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Reportes Rápidos</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button variant="outline" className="w-full justify-start">
              <Download className="w-4 h-4 mr-2" />
              Reporte Mensual PDF
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Download className="w-4 h-4 mr-2" />
              Exportar Contactos CSV
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <Download className="w-4 h-4 mr-2" />
              Análisis de Servicios
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Métricas Clave</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-600 dark:text-slate-400">ROI Promedio</span>
              <span className="font-semibold text-slate-900 dark:text-white">340%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-600 dark:text-slate-400">Valor Cliente</span>
              <span className="font-semibold text-slate-900 dark:text-white">€2,450</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-slate-600 dark:text-slate-400">Retención</span>
              <span className="font-semibold text-slate-900 dark:text-white">78%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Próximas Acciones</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center space-x-2 text-sm">
              <Activity className="w-4 h-4 text-blue-600" />
              <span>Seguimiento 12 leads pendientes</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <Mail className="w-4 h-4 text-green-600" />
              <span>Enviar newsletter mensual</span>
            </div>
            <div className="flex items-center space-x-2 text-sm">
              <Target className="w-4 h-4 text-orange-600" />
              <span>Revisar objetivos Q2</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Notice */}
      <Card className="mt-6 border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <TrendingUp className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-green-900 dark:text-green-100">
                Reportes Basados en Datos Reales
              </h3>
              <p className="text-sm text-green-800 dark:text-green-200 mt-1">
                Los datos mostrados son ejemplos representativos. En producción, 
                todos los reportes se generan automáticamente desde la base de datos 
                de Supabase con métricas en tiempo real.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
