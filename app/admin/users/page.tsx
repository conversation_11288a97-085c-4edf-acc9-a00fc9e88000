import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Users, 
  Search, 
  Plus, 
  Shield, 
  Mail, 
  Calendar,
  Settings,
  UserCheck,
  UserX
} from 'lucide-react'
import Link from 'next/link'

// Datos de ejemplo de usuarios (en una implementación real vendrían de Supabase)
const users = [
  {
    id: 1,
    name: 'Admin Principal',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastLogin: '2025-06-24T06:00:00Z',
    createdAt: '2025-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    email: '<EMAIL>',
    role: 'manager',
    status: 'active',
    lastLogin: '2025-06-23T15:30:00Z',
    createdAt: '2025-02-15T00:00:00Z'
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'support',
    status: 'active',
    lastLogin: '2025-06-22T09:15:00Z',
    createdAt: '2025-03-01T00:00:00Z'
  }
]

const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    case 'manager': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    case 'support': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    case 'suspended': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function UsersPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Gestión de Usuarios
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            Administra los usuarios del sistema Karedesk
          </p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Nuevo Usuario
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Total Usuarios
            </CardTitle>
            <Users className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {users.length}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Usuarios registrados
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Usuarios Activos
            </CardTitle>
            <UserCheck className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {users.filter(u => u.status === 'active').length}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Con acceso al sistema
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Administradores
            </CardTitle>
            <Shield className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {users.filter(u => u.role === 'admin').length}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Con permisos completos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Nuevos Este Mes
            </CardTitle>
            <Calendar className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              0
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Registros recientes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filtros y Búsqueda</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Buscar por nombre, email o rol..."
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Filtros Avanzados
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Lista de Usuarios</CardTitle>
          <CardDescription>
            Gestiona los usuarios y sus permisos en el sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {users.map((user) => (
              <div key={user.id} className="flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-slate-900 dark:text-white">
                        {user.name}
                      </h3>
                      <Badge className={getRoleColor(user.role)}>
                        {user.role === 'admin' ? 'Administrador' : 
                         user.role === 'manager' ? 'Gestor' : 
                         user.role === 'support' ? 'Soporte' : user.role}
                      </Badge>
                      <Badge className={getStatusColor(user.status)}>
                        {user.status === 'active' ? 'Activo' : 
                         user.status === 'inactive' ? 'Inactivo' : 
                         user.status === 'suspended' ? 'Suspendido' : user.status}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400">
                      <div className="flex items-center space-x-1">
                        <Mail className="w-4 h-4" />
                        <span>{user.email}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>Último acceso: {formatDate(user.lastLogin)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    Editar
                  </Button>
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4" />
                  </Button>
                  {user.status === 'active' ? (
                    <Button variant="outline" size="sm">
                      <UserX className="w-4 h-4" />
                    </Button>
                  ) : (
                    <Button variant="outline" size="sm">
                      <UserCheck className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Notice */}
      <Card className="mt-6 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                Gestión de Usuarios en Desarrollo
              </h3>
              <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                Esta sección está en desarrollo. Actualmente muestra datos de ejemplo. 
                La funcionalidad completa de gestión de usuarios se implementará próximamente 
                con integración completa a Supabase Auth.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
