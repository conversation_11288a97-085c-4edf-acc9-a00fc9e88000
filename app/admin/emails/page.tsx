import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { 
  Mail, 
  Send, 
  Plus, 
  Search, 
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  FileText,
  Settings
} from 'lucide-react'

// Datos de ejemplo de emails (en una implementación real vendrían de Supabase)
const emailTemplates = [
  {
    id: 1,
    name: 'Bienvenida Nuevo Cliente',
    subject: 'Bienvenido a Karedesk - Tu socio tecnológico',
    type: 'welcome',
    status: 'active',
    lastUsed: '2025-06-23T10:30:00Z',
    timesUsed: 45
  },
  {
    id: 2,
    name: 'Confirmación de Consulta',
    subject: 'Hemos recibido tu consulta - Karedesk',
    type: 'confirmation',
    status: 'active',
    lastUsed: '2025-06-24T06:17:00Z',
    timesUsed: 128
  },
  {
    id: 3,
    name: 'Seguimiento de Proyecto',
    subject: 'Actualización de tu proyecto con Karedesk',
    type: 'follow-up',
    status: 'active',
    lastUsed: '2025-06-22T15:45:00Z',
    timesUsed: 67
  }
]

const recentEmails = [
  {
    id: 1,
    to: '<EMAIL>',
    subject: 'Re: Desarrollo Web - Propuesta personalizada',
    status: 'sent',
    sentAt: '2025-06-24T06:20:00Z',
    template: 'Confirmación de Consulta'
  },
  {
    id: 2,
    to: '<EMAIL>',
    subject: 'Bienvenido a Karedesk - Tu consulta de Consultoría IT',
    status: 'sent',
    sentAt: '2025-06-24T06:18:00Z',
    template: 'Bienvenida Nuevo Cliente'
  },
  {
    id: 3,
    to: '<EMAIL>',
    subject: 'Confirmación de recepción - Desarrollo Web',
    status: 'failed',
    sentAt: '2025-06-24T06:15:00Z',
    template: 'Confirmación de Consulta'
  }
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'sent': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'sent': return <CheckCircle className="w-4 h-4" />
    case 'failed': return <XCircle className="w-4 h-4" />
    case 'pending': return <Clock className="w-4 h-4" />
    default: return <Mail className="w-4 h-4" />
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('es-ES', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export default function EmailsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">
            Gestión de Emails
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            Administra templates y envía emails a tus clientes
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            Nuevo Template
          </Button>
          <Button>
            <Send className="w-4 h-4 mr-2" />
            Enviar Email
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Emails Enviados Hoy
            </CardTitle>
            <Send className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {recentEmails.filter(e => e.status === 'sent').length}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Entregados exitosamente
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Templates Activos
            </CardTitle>
            <FileText className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              {emailTemplates.filter(t => t.status === 'active').length}
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Listos para usar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Tasa de Entrega
            </CardTitle>
            <CheckCircle className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              67%
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Últimos 30 días
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
              Contactos Únicos
            </CardTitle>
            <Users className="h-4 w-4 text-slate-600 dark:text-slate-400" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-slate-900 dark:text-white">
              156
            </div>
            <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
              Clientes contactados
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Email Templates */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Templates de Email
            </CardTitle>
            <CardDescription>
              Gestiona tus plantillas de email personalizadas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {emailTemplates.map((template) => (
                <div key={template.id} className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-medium text-slate-900 dark:text-white">
                        {template.name}
                      </h3>
                      <Badge className={getStatusColor(template.status)}>
                        {template.status === 'active' ? 'Activo' : 'Borrador'}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                      {template.subject}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-slate-500 dark:text-slate-500 mt-2">
                      <span>Usado {template.timesUsed} veces</span>
                      <span>Último uso: {formatDate(template.lastUsed)}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      Editar
                    </Button>
                    <Button variant="outline" size="sm">
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                <Plus className="w-4 h-4 mr-2" />
                Crear Nuevo Template
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Recent Emails */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="w-5 h-5" />
              Emails Recientes
            </CardTitle>
            <CardDescription>
              Historial de emails enviados recientemente
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentEmails.map((email) => (
                <div key={email.id} className="flex items-start space-x-3 p-3 border border-slate-200 dark:border-slate-700 rounded-lg">
                  <div className="flex-shrink-0 mt-1">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      email.status === 'sent' ? 'bg-green-100 dark:bg-green-900' :
                      email.status === 'failed' ? 'bg-red-100 dark:bg-red-900' :
                      'bg-yellow-100 dark:bg-yellow-900'
                    }`}>
                      {getStatusIcon(email.status)}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-slate-900 dark:text-white truncate">
                        {email.to}
                      </p>
                      <Badge className={getStatusColor(email.status)}>
                        {email.status === 'sent' ? 'Enviado' : 
                         email.status === 'failed' ? 'Falló' : 'Pendiente'}
                      </Badge>
                    </div>
                    <p className="text-sm text-slate-600 dark:text-slate-400 truncate">
                      {email.subject}
                    </p>
                    <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-500 mt-1">
                      <span>{email.template}</span>
                      <span>{formatDate(email.sentAt)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4">
              <Button variant="outline" className="w-full">
                Ver Historial Completo
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Send */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="w-5 h-5" />
            Envío Rápido
          </CardTitle>
          <CardDescription>
            Envía un email personalizado rápidamente
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Destinatario
                </label>
                <Input placeholder="<EMAIL>" />
              </div>
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Asunto
                </label>
                <Input placeholder="Asunto del email" />
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Mensaje
                </label>
                <Textarea 
                  placeholder="Escribe tu mensaje aquí..."
                  rows={4}
                />
              </div>
            </div>
          </div>
          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4 mr-2" />
                Opciones Avanzadas
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline">
                Guardar Borrador
              </Button>
              <Button>
                <Send className="w-4 h-4 mr-2" />
                Enviar Ahora
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notice */}
      <Card className="mt-6 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-blue-900 dark:text-blue-100">
                Sistema de Emails Integrado
              </h3>
              <p className="text-sm text-blue-800 dark:text-blue-200 mt-1">
                El sistema de emails está integrado con Resend (API Key configurada). 
                Los templates mostrados son de ejemplo. La funcionalidad completa incluye 
                envío automático de confirmaciones y seguimientos personalizados.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
