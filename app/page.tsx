"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowRight, CheckCircle, Wrench, Shield, Brain, Code, Mail, Phone, MapPin, Menu, X, Star, ChevronDown, Globe, Bug, User, Zap } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion, useScroll, useTransform, useInView, AnimatePresence } from "framer-motion"
import { useRef, useState, useEffect } from "react"
import ContactForm from "@/components/contact-form"

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const scaleOnHover = {
  whileHover: {
    scale: 1.05,
    transition: { duration: 0.2 },
  },
  whileTap: { scale: 0.95 },
}

// Custom hook for scroll spy
const useScrollSpy = (sectionIds: string[], offset = 100) => {
  const [activeSection, setActiveSection] = useState('')

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + offset

      for (const sectionId of sectionIds) {
        const element = document.getElementById(sectionId)
        if (element) {
          const { offsetTop, offsetHeight } = element
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(sectionId)
            break
          }
        }
      }
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // Check initial position

    return () => window.removeEventListener('scroll', handleScroll)
  }, [sectionIds, offset])

  return activeSection
}

// Floating animation for hero elements
const floatingAnimation = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut" as const,
    },
  },
}

// Animated counter component
function AnimatedCounter({ end, duration = 2, suffix = "" }: { end: number; duration?: number; suffix?: string }) {
  const [count, setCount] = useState(0)
  const ref = useRef(null)
  const isInView = useInView(ref)

  useEffect(() => {
    if (isInView) {
      let startTime: number
      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime
        const progress = Math.min((currentTime - startTime) / (duration * 1000), 1)
        setCount(Math.floor(progress * end))
        if (progress < 1) {
          requestAnimationFrame(animate)
        }
      }
      requestAnimationFrame(animate)
    }
  }, [isInView, end, duration])

  return (
    <span ref={ref}>
      {count}
      {suffix}
    </span>
  )
}

export default function KaredeskLanding() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [servicesDropdownOpen, setServicesDropdownOpen] = useState(false)
  const { scrollYProgress } = useScroll()
  const heroRef = useRef(null)

  // Scroll spy for active section
  const activeSection = useScrollSpy(['hero', 'servicios', 'nosotros', 'contacto'])

  // Parallax effects
  const yText = useTransform(scrollYProgress, [0, 1], ["0%", "200%"])

  // Services data
  const services = [
    {
      name: "Consultoría IA",
      href: "/servicios/consultoria-ia",
      icon: Brain,
      description: "Implementación de soluciones de inteligencia artificial"
    },
    {
      name: "Creación Web",
      href: "/servicios/creacion-web",
      icon: Globe,
      description: "Desarrollo de sitios web modernos y responsivos"
    },
    {
      name: "Análisis de Vulnerabilidades",
      href: "/servicios/analisis-vulnerabilidades",
      icon: Bug,
      description: "Auditorías de seguridad y análisis de riesgos"
    },
    {
      name: "Asistencia Informática",
      href: "/servicios/asistencia-informatica",
      icon: Wrench,
      description: "Soporte técnico y mantenimiento de sistemas"
    }
  ]

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.services-dropdown')) {
        setServicesDropdownOpen(false)
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [])

  // Smooth scroll function
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
    }
    setMobileMenuOpen(false)
    setServicesDropdownOpen(false)
  }

  return (
    <div className="min-h-screen bg-hero-gradient overflow-x-hidden font-inter">
      {/* Animated background elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-brand-primary/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-brand-accent/10 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 10,
            repeat: Number.POSITIVE_INFINITY,
            ease: "easeInOut",
          }}
        />
      </div>

      {/* Navigation */}
      <motion.nav
        className="fixed top-0 w-full z-50 bg-slate-900/90 backdrop-blur-xl border-b border-slate-700/30"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo - Premium Enhanced */}
            <motion.div
              className="flex items-center group cursor-pointer relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.98 }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 17
              }}
              onClick={() => scrollToSection('hero')}
            >
              <div className="relative">
                {/* Animated background glow */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-brand-primary/30 via-purple-500/20 to-brand-secondary/30 rounded-full blur-xl"
                  animate={{
                    opacity: [0, 0.3, 0],
                    scale: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                {/* Hover glow effect */}
                <div className="absolute inset-0 bg-brand-primary/40 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-all duration-500 group-hover:scale-110" />

                {/* Logo with enhanced effects */}
                <motion.div
                  className="relative z-10"
                  whileHover={{ rotate: [0, -5, 5, 0] }}
                  transition={{ duration: 0.6 }}
                >
                  <Image
                    src="/logo.png"
                    alt="Karedesk"
                    width={40}
                    height={40}
                    className="w-10 h-10 object-contain filter drop-shadow-2xl group-hover:drop-shadow-[0_0_20px_rgba(92,158,173,0.5)] transition-all duration-300"
                    priority
                  />
                </motion.div>

                {/* Subtle ring animation */}
                <motion.div
                  className="absolute inset-0 border-2 border-brand-primary/20 rounded-full"
                  animate={{
                    scale: [1, 1.1, 1],
                    opacity: [0.3, 0.1, 0.3],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </div>
            </motion.div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              {/* Services Dropdown */}
              <div className="relative services-dropdown">
                <motion.button
                  onClick={() => setServicesDropdownOpen(!servicesDropdownOpen)}
                  className={`relative px-4 py-2 text-slate-300 hover:text-white transition-all duration-300 font-medium text-sm rounded-xl hover:bg-slate-800/50 flex items-center gap-1 ${
                    activeSection === 'servicios' ? 'text-white' : ''
                  }`}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  whileHover={{ y: -1 }}
                >
                  Servicios
                  <ChevronDown
                    className={`w-3 h-3 transition-transform duration-200 ${
                      servicesDropdownOpen ? 'rotate-180' : ''
                    }`}
                  />
                  <motion.div
                    className={`absolute bottom-0 left-1/2 h-0.5 bg-gradient-to-r from-brand-primary to-brand-accent rounded-full ${
                      activeSection === 'servicios' ? 'w-4/5' : 'w-0'
                    }`}
                    style={{ x: '-50%' }}
                    animate={{ width: activeSection === 'servicios' ? '80%' : '0%' }}
                    transition={{ duration: 0.3 }}
                  />
                </motion.button>

                {/* Services Dropdown Menu */}
                <AnimatePresence>
                  {servicesDropdownOpen && (
                    <motion.div
                      className="absolute top-full left-0 mt-2 w-80 bg-slate-900/95 backdrop-blur-xl border border-slate-700/50 rounded-2xl shadow-2xl overflow-hidden"
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                    >
                      <div className="p-2">
                        {services.map((service, index) => (
                          <motion.div
                            key={service.name}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.05 }}
                          >
                            <Link
                              href={service.href}
                              className="flex items-start gap-3 p-3 rounded-xl hover:bg-slate-800/50 transition-all duration-300 group"
                              onClick={() => setServicesDropdownOpen(false)}
                            >
                              <div className="w-8 h-8 bg-gradient-to-r from-brand-primary to-brand-accent rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                                <service.icon className="w-4 h-4 text-white" />
                              </div>
                              <div className="flex-1">
                                <h4 className="text-white font-medium text-sm group-hover:text-brand-primary transition-colors">
                                  {service.name}
                                </h4>
                                <p className="text-slate-400 text-xs mt-1 leading-relaxed">
                                  {service.description}
                                </p>
                              </div>
                            </Link>
                          </motion.div>
                        ))}
                        <motion.div
                          className="mt-2 pt-2 border-t border-slate-700/50"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.2 }}
                        >
                          <button
                            onClick={() => scrollToSection('servicios')}
                            className="w-full text-left p-3 rounded-xl hover:bg-slate-800/50 transition-all duration-300 text-brand-primary text-sm font-medium"
                          >
                            Ver todos los servicios →
                          </button>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Other Navigation Items */}
              {[
                { name: "Nosotros", id: "nosotros" },
                { name: "Contacto", id: "contacto" },
              ].map((item, index) => (
                <motion.button
                  key={item.name}
                  onClick={() => scrollToSection(item.id)}
                  className={`relative px-4 py-2 text-slate-300 hover:text-white transition-all duration-300 font-medium text-sm rounded-xl hover:bg-slate-800/50 ${
                    activeSection === item.id ? 'text-white' : ''
                  }`}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: (index + 1) * 0.1 + 0.4 }}
                  whileHover={{ y: -1 }}
                >
                  {item.name}
                  <motion.div
                    className={`absolute bottom-0 left-1/2 h-0.5 bg-gradient-to-r from-brand-primary to-brand-accent rounded-full ${
                      activeSection === item.id ? 'w-4/5' : 'w-0'
                    }`}
                    style={{ x: '-50%' }}
                    animate={{ width: activeSection === item.id ? '80%' : '0%' }}
                    transition={{ duration: 0.3 }}
                  />
                </motion.button>
              ))}

              {/* Admin Link */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 }}
              >
                <Link
                  href="/login"
                  className="p-2 text-slate-400 hover:text-white transition-all duration-300 rounded-xl hover:bg-slate-800/50"
                  title="Panel de Administración"
                >
                  <User className="w-4 h-4" />
                </Link>
              </motion.div>

              <div className="ml-4 pl-4 border-l border-slate-700">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.8 }}
                >
                  <motion.div {...scaleOnHover}>
                    <Button
                      variant="gradient"
                      size="sm"
                      onClick={() => scrollToSection("contacto")}
                      className="shadow-lg hover:shadow-brand/25 font-semibold"
                    >
                      Consulta Gratuita
                    </Button>
                  </motion.div>
                </motion.div>
              </div>
            </div>

            {/* Mobile menu button */}
            <motion.button
              className="md:hidden p-2 text-white hover:bg-slate-800/50 rounded-xl transition-colors"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              whileTap={{ scale: 0.95 }}
            >
              <AnimatePresence mode="wait">
                {mobileMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="w-5 h-5" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="w-5 h-5" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>

        {/* Mobile menu */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div
              className="md:hidden bg-slate-900/95 backdrop-blur-xl border-t border-slate-700/30"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="container mx-auto px-6 py-6 space-y-3">
                {/* Services Section */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0 }}
                >
                  <button
                    onClick={() => scrollToSection('servicios')}
                    className="block w-full text-left px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/50 transition-all duration-300 font-medium rounded-xl"
                  >
                    Servicios
                  </button>
                  <div className="ml-4 mt-2 space-y-2">
                    {services.map((service, index) => (
                      <motion.div
                        key={service.name}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 + 0.1 }}
                      >
                        <Link
                          href={service.href}
                          className="flex items-center gap-3 px-4 py-2 text-slate-400 hover:text-white hover:bg-slate-800/30 transition-all duration-300 rounded-lg text-sm"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          <service.icon className="w-4 h-4" />
                          {service.name}
                        </Link>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>

                {/* Other Navigation Items */}
                {[
                  { name: "Nosotros", id: "nosotros" },
                  { name: "Contacto", id: "contacto" },
                ].map((item, index) => (
                  <motion.button
                    key={item.name}
                    onClick={() => scrollToSection(item.id)}
                    className="block w-full text-left px-4 py-3 text-slate-300 hover:text-white hover:bg-slate-800/50 transition-all duration-300 font-medium rounded-xl"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (index + 1) * 0.1 + 0.2 }}
                  >
                    {item.name}
                  </motion.button>
                ))}
                {/* Admin Link */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <Link
                    href="/login"
                    className="flex items-center gap-3 px-4 py-3 text-slate-400 hover:text-white hover:bg-slate-800/50 transition-all duration-300 rounded-xl"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <User className="w-4 h-4" />
                    Panel de Administración
                  </Link>
                </motion.div>

                <motion.div
                  className="pt-3 border-t border-slate-700/50"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Button variant="gradient" className="w-full font-semibold" onClick={() => scrollToSection("contacto")}>
                    Consulta Gratuita
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.nav>

      {/* Hero Section */}
      <section id="hero" ref={heroRef} className="pt-32 pb-20 px-4 relative">
        <motion.div className="container mx-auto text-center" style={{ y: yText }}>
          <motion.div className="mb-8" {...floatingAnimation}>
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
            >
              <Image src="/logo.png" alt="Karedesk" width={120} height={120} className="w-30 h-30 mx-auto mb-6" />
            </motion.div>
          </motion.div>

          <motion.h1
            className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight tracking-tight"
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Soluciones Digitales
            <motion.span
              className="block text-gradient-brand"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
            >
              del Futuro
            </motion.span>
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-slate-300 mb-8 max-w-4xl mx-auto leading-relaxed font-light"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
          >
            Transformamos tu negocio con tecnología de vanguardia. Especialistas en seguridad web, inteligencia
            artificial y desarrollo digital.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.9 }}
          >
            <motion.div {...scaleOnHover}>
              <Button size="lg" variant="gradient" className="group" onClick={() => scrollToSection("contacto")}>
                Comenzar Ahora
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
              </Button>
            </motion.div>
            <motion.div {...scaleOnHover}>
              <Button size="lg" variant="outline" onClick={() => scrollToSection("servicios")}>
                Ver Servicios
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Services Section */}
      <motion.section
        id="servicios"
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight">Nuestros Servicios</h2>
            <p className="text-xl md:text-2xl text-slate-300 max-w-3xl mx-auto font-light leading-relaxed">
              Ofrecemos soluciones integrales para impulsar tu presencia digital y proteger tu negocio
            </p>
          </motion.div>

          <motion.div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8" variants={staggerContainer}>
            {[
              {
                icon: Wrench,
                title: "Asistencia Informática",
                description: "🚀 Soporte técnico premium que mantiene tu negocio funcionando sin interrupciones. ¡Ahorra hasta 70% en costos de TI!",
                features: ["✅ Soporte 24/7/365", "🔧 Mantenimiento preventivo", "⚡ Resolución en <2h"],
                href: "/servicios/asistencia-informatica",
                variant: "tech" as const,
              },
              {
                icon: Shield,
                title: "Análisis de Vulnerabilidades",
                description: "Auditorías de seguridad completas para proteger tu sitio web de amenazas",
                features: ["Pentesting avanzado", "Reportes detallados", "Recomendaciones"],
                href: "/servicios/analisis-vulnerabilidades",
                variant: "security" as const,
              },
              {
                icon: Brain,
                title: "Consultoría IA",
                description: "Implementación de inteligencia artificial para optimizar tus procesos de negocio",
                features: ["Automatización", "Machine Learning", "Análisis predictivo"],
                href: "/servicios/consultoria-ia",
                variant: "ai" as const,
              },
              {
                icon: Code,
                title: "Creación de Páginas Web",
                description: "Desarrollo de sitios web modernos, rápidos y optimizados para conversión",
                features: ["Diseño responsive", "SEO optimizado", "E-commerce"],
                href: "/servicios/creacion-web",
                variant: "web" as const,
              },
            ].map((service, index) => (
              <motion.div
                key={service.title}
                variants={fadeInUp}
                whileHover={{
                  y: -10,
                  transition: { duration: 0.3 },
                }}
              >
                <Link href={service.href}>
                  <Card className={`card-${service.variant} h-full cursor-pointer`}>
                    <CardHeader className="p-8">
                      <motion.div
                        className={`w-16 h-16 icon-${service.variant} rounded-2xl flex items-center justify-center mb-6`}
                        whileHover={{
                          rotate: 360,
                          transition: { duration: 0.6 },
                        }}
                      >
                        <service.icon className="w-8 h-8" />
                      </motion.div>
                      <CardTitle className="text-white text-xl font-bold mb-3 tracking-tight">
                        {service.title}
                      </CardTitle>
                      <CardDescription className="text-slate-400 font-light leading-relaxed">
                        {service.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="px-8 pb-8">
                      <ul className="space-y-3 text-slate-300 mb-6">
                        {service.features.map((feature, featureIndex) => (
                          <motion.li
                            key={feature}
                            className="flex items-center font-medium"
                            initial={{ opacity: 0, x: -20 }}
                            whileInView={{ opacity: 1, x: 0 }}
                            transition={{ delay: featureIndex * 0.1 }}
                          >
                            <CheckCircle className="w-4 h-4 text-brand-primary mr-3" />
                            {feature}
                          </motion.li>
                        ))}
                      </ul>
                      <Button
                        variant="ghost"
                        className="text-brand-primary hover:text-brand-secondary p-0 h-auto font-semibold group-hover:translate-x-2 transition-transform"
                      >
                        Ver detalles
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </CardContent>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* Asistencia Informática CTA Section */}
      <motion.section
        className="py-20 px-4 bg-gradient-to-r from-brand-primary/10 to-brand-accent/10"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div variants={fadeInLeft}>
              <div className="inline-flex items-center gap-2 bg-brand-primary/20 text-brand-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
                <Wrench className="w-4 h-4" />
                Servicio Estrella
              </div>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
                ¿Problemas con tu <span className="text-brand-primary">infraestructura IT</span>?
              </h2>
              <p className="text-xl text-slate-300 mb-8 leading-relaxed">
                Nuestro servicio de <strong>Asistencia Informática Premium</strong> mantiene tu negocio funcionando 24/7.
                Ahorra hasta un <span className="text-brand-primary font-bold">70% en costos</span> comparado con un equipo interno.
              </p>

              <div className="grid md:grid-cols-2 gap-6 mb-8">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-green-400 text-lg">✓</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Respuesta en menos de 2 horas</h4>
                    <p className="text-slate-400 text-sm">Soporte técnico inmediato cuando lo necesites</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-green-400 text-lg">✓</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Mantenimiento preventivo</h4>
                    <p className="text-slate-400 text-sm">Evitamos problemas antes de que ocurran</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-green-400 text-lg">✓</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Equipo especializado</h4>
                    <p className="text-slate-400 text-sm">Técnicos certificados en múltiples tecnologías</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-green-400 text-lg">✓</span>
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-1">Monitoreo 24/7</h4>
                    <p className="text-slate-400 text-sm">Vigilancia continua de tu infraestructura</p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  variant="tech"
                  size="lg"
                  className="group"
                  onClick={() => document.getElementById('contacto')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  <Wrench className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
                  Solicitar Consulta Gratuita
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => window.open('tel:+34123456789')}
                >
                  <Phone className="w-5 h-5 mr-2" />
                  Llamar Ahora
                </Button>
              </div>
            </motion.div>

            <motion.div variants={fadeInRight} className="relative">
              <div className="relative bg-slate-900/50 backdrop-blur-xl border border-slate-700/30 rounded-3xl p-8">
                <div className="text-center mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-primary/20 rounded-2xl mb-4">
                    <Wrench className="w-8 h-8 text-brand-primary" />
                  </div>
                  <h3 className="text-2xl font-bold text-white mb-2">Oferta Especial</h3>
                  <p className="text-slate-300">Para nuevos clientes</p>
                </div>

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between items-center py-3 border-b border-slate-700/50">
                    <span className="text-slate-300">Diagnóstico completo</span>
                    <span className="text-brand-primary font-semibold">GRATIS</span>
                  </div>
                  <div className="flex justify-between items-center py-3 border-b border-slate-700/50">
                    <span className="text-slate-300">Primera intervención</span>
                    <span className="text-brand-primary font-semibold">50% DESC</span>
                  </div>
                  <div className="flex justify-between items-center py-3">
                    <span className="text-slate-300">Soporte 24/7 primer mes</span>
                    <span className="text-brand-primary font-semibold">INCLUIDO</span>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-slate-400 text-sm mb-4">
                    💰 Ahorro promedio: <span className="text-brand-primary font-semibold">€2,500/mes</span>
                  </p>
                  <p className="text-slate-500 text-xs">
                    *Oferta válida hasta fin de mes. Términos y condiciones aplicables.
                  </p>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Why Choose Us */}
      <motion.section
        id="nosotros"
        className="pt-32 pb-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div variants={fadeInLeft}>
              <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight">
                ¿Por qué elegir Karedesk?
              </h2>
              <p className="text-xl md:text-2xl text-slate-300 mb-8 font-light leading-relaxed">
                Somos más que una empresa de tecnología. Somos tu socio estratégico en la transformación digital.
              </p>
              <div className="space-y-6">
                {[
                  {
                    title: "Experiencia Comprobada",
                    description: "Más de 5 años ayudando a empresas a crecer digitalmente",
                  },
                  {
                    title: "Tecnología de Vanguardia",
                    description: "Utilizamos las últimas herramientas y metodologías del mercado",
                  },
                  {
                    title: "Soporte Personalizado",
                    description: "Atención dedicada y soluciones adaptadas a tu negocio",
                  },
                ].map((item, index) => (
                  <motion.div
                    key={item.title}
                    className="flex items-start space-x-4"
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-brand-primary rounded-2xl flex items-center justify-center flex-shrink-0 mt-1"
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ duration: 0.3 }}
                    >
                      <CheckCircle className="w-5 h-5 text-white" />
                    </motion.div>
                    <div>
                      <h3 className="text-white font-bold text-lg mb-2 tracking-tight">{item.title}</h3>
                      <p className="text-slate-400 font-light leading-relaxed">{item.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div className="relative" variants={fadeInRight}>
              <motion.div
                className="bg-gradient-to-br from-brand-primary/20 to-brand-accent/20 rounded-3xl p-8 backdrop-blur-sm border border-slate-700"
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.3 }}
              >
                <div className="grid grid-cols-2 gap-8">
                  {[
                    { number: 100, suffix: "+", label: "Proyectos Completados" },
                    { number: 24, suffix: "/7", label: "Soporte Disponible" },
                    { number: 99, suffix: "%", label: "Satisfacción Cliente" },
                    { number: 5, suffix: "+", label: "Años Experiencia" },
                  ].map((stat, index) => (
                    <motion.div
                      key={stat.label}
                      className="text-center"
                      initial={{ opacity: 0, scale: 0.5 }}
                      whileInView={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 }}
                      whileHover={{ scale: 1.1 }}
                    >
                      <div className="text-4xl font-bold text-brand-primary mb-2 tracking-tight">
                        <AnimatedCounter end={stat.number} />
                        {stat.suffix}
                      </div>
                      <div className="text-slate-300 font-medium">{stat.label}</div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Testimonials */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight">
              Lo que dicen nuestros clientes
            </h2>
          </motion.div>

          <motion.div className="grid md:grid-cols-3 gap-8" variants={staggerContainer}>
            {[
              { name: "Juan Pérez", role: "CEO, TechCorp", initials: "JP" },
              { name: "María García", role: "CTO, InnovaCorp", initials: "MG" },
              { name: "Carlos López", role: "Director, DigitalPro", initials: "CL" },
            ].map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                variants={fadeInUp}
                whileHover={{
                  y: -5,
                  transition: { duration: 0.3 },
                }}
              >
                <Card className="h-full">
                  <CardContent className="p-8">
                    <motion.div
                      className="flex items-center mb-6"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      {[1, 2, 3, 4, 5].map((star) => (
                        <motion.div
                          key={star}
                          initial={{ opacity: 0, scale: 0 }}
                          whileInView={{ opacity: 1, scale: 1 }}
                          transition={{ delay: star * 0.1 }}
                        >
                          <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        </motion.div>
                      ))}
                    </motion.div>
                    <p className="text-slate-300 mb-6 font-light leading-relaxed text-lg">
                      "Karedesk transformó completamente nuestra presencia digital. Su equipo es profesional y los
                      resultados superaron nuestras expectativas."
                    </p>
                    <div className="flex items-center">
                      <motion.div
                        className="w-12 h-12 bg-brand-primary rounded-2xl flex items-center justify-center mr-4"
                        whileHover={{ scale: 1.1, rotate: 360 }}
                        transition={{ duration: 0.3 }}
                      >
                        <span className="text-white font-bold">{testimonial.initials}</span>
                      </motion.div>
                      <div>
                        <div className="text-white font-bold tracking-tight">{testimonial.name}</div>
                        <div className="text-slate-400 font-medium">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* CTA Section */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto text-center">
          <motion.div
            className="bg-gradient-to-r from-brand-primary/20 to-brand-accent/20 rounded-3xl p-12 backdrop-blur-sm border border-slate-700"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.3 }}
          >
            <motion.h2
              className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              ¿Listo para transformar tu negocio?
            </motion.h2>
            <motion.p
              className="text-xl md:text-2xl text-slate-300 mb-8 max-w-3xl mx-auto font-light leading-relaxed"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Obtén una consulta gratuita y descubre cómo podemos impulsar tu crecimiento digital
            </motion.p>
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <motion.div {...scaleOnHover}>
                <Button
                  size="lg"
                  variant="gradient"
                  className="group"
                  onClick={() => scrollToSection("contacto")}
                >
                Consulta Gratuita
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Number.POSITIVE_INFINITY }}
                >
                  <ArrowRight className="w-5 h-5" />
                </motion.div>
              </Button>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>

      {/* Contact Section */}
      <motion.section
        id="contacto"
        className="pt-32 pb-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true, margin: "-100px" }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-6xl font-bold text-white mb-6 tracking-tight">Contáctanos</h2>
            <p className="text-xl md:text-2xl text-slate-300 font-light">
              Estamos aquí para ayudarte a alcanzar tus objetivos digitales
            </p>
          </motion.div>

          {/* Contact Form */}
          <div className="mb-16">
            <ContactForm
              service="Consulta General"
              title="¿Listo para comenzar tu transformación digital?"
              description="Completa el formulario y te contactaremos en menos de 24 horas para discutir tu proyecto"
              showBudget={true}
              showTimeline={true}
            />
          </div>

          {/* Contact Info */}
          <motion.div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto" variants={staggerContainer}>
            {[
              {
                icon: Mail,
                title: "Email",
                info: "<EMAIL>",
                variant: "tech" as const,
                action: () => window.open("mailto:<EMAIL>"),
              },
              {
                icon: Phone,
                title: "Teléfono",
                info: "+34 123 456 789",
                variant: "tech" as const,
                action: () => window.open("tel:+34123456789"),
              },
              {
                icon: MapPin,
                title: "Ubicación",
                info: "Madrid, España",
                variant: "tech" as const,
                action: () => {},
              },
            ].map((contact, index) => (
              <motion.div key={contact.title} className="text-center" variants={fadeInUp} whileHover={{ y: -5 }}>
                <Card className="card-tech cursor-pointer" onClick={contact.action}>
                  <CardContent className="p-8">
                    <motion.div
                      className="w-16 h-16 icon-tech rounded-2xl flex items-center justify-center mx-auto mb-6"
                      whileHover={{
                        scale: 1.1,
                        rotate: 360,
                        transition: { duration: 0.6 },
                      }}
                    >
                      <contact.icon className="w-8 h-8" />
                    </motion.div>
                    <h3 className="text-white font-bold text-lg mb-3 tracking-tight">{contact.title}</h3>
                    <p className="text-slate-400 font-medium">{contact.info}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.section>

      {/* Footer */}
      <motion.footer
        className="bg-slate-900 py-16 px-4"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
      >
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            {/* Company Info */}
            <motion.div
              className="col-span-1 md:col-span-2"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
            >
              <div className="flex items-center space-x-3 mb-6">
                <Image
                  src="/logo.png"
                  alt="Karedesk"
                  width={40}
                  height={40}
                  className="w-10 h-10"
                />
                <span className="text-white font-bold text-xl">Karedesk</span>
              </div>
              <p className="text-slate-400 mb-6 max-w-md leading-relaxed">
                Transformamos tu negocio con tecnología de vanguardia. Especialistas en seguridad web,
                inteligencia artificial y desarrollo digital.
              </p>
              <div className="flex space-x-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  className="text-slate-400 hover:text-brand-primary transition-colors"
                  whileHover={{ scale: 1.1 }}
                >
                  <Mail className="w-5 h-5" />
                </motion.a>
                <motion.a
                  href="tel:+34123456789"
                  className="text-slate-400 hover:text-brand-primary transition-colors"
                  whileHover={{ scale: 1.1 }}
                >
                  <Phone className="w-5 h-5" />
                </motion.a>
              </div>
            </motion.div>

            {/* Services */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <h3 className="text-white font-semibold mb-4">Servicios</h3>
              <ul className="space-y-2">
                <li><a href="/servicios/asistencia-informatica" className="text-slate-400 hover:text-brand-primary transition-colors">Asistencia Informática</a></li>
                <li><a href="/servicios/analisis-vulnerabilidades" className="text-slate-400 hover:text-brand-primary transition-colors">Análisis de Vulnerabilidades</a></li>
                <li><a href="/servicios/consultoria-ia" className="text-slate-400 hover:text-brand-primary transition-colors">Consultoría IA</a></li>
                <li><a href="/servicios/creacion-web" className="text-slate-400 hover:text-brand-primary transition-colors">Creación Web</a></li>
              </ul>
            </motion.div>

            {/* Legal */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
            >
              <h3 className="text-white font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><a href="/legal/privacidad" className="text-slate-400 hover:text-brand-primary transition-colors">Política de Privacidad</a></li>
                <li><a href="/legal/terminos" className="text-slate-400 hover:text-brand-primary transition-colors">Términos de Servicio</a></li>
                <li><a href="/legal/cookies" className="text-slate-400 hover:text-brand-primary transition-colors">Política de Cookies</a></li>
                <li><a href="/legal/aviso-legal" className="text-slate-400 hover:text-brand-primary transition-colors">Aviso Legal</a></li>
              </ul>
            </motion.div>
          </div>

          {/* Bottom Bar */}
          <motion.div
            className="pt-8 border-t border-slate-700/50 flex flex-col md:flex-row justify-between items-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4 }}
          >
            <p className="text-slate-400 text-sm mb-4 md:mb-0">
              © 2024 Karedesk. Todos los derechos reservados.
            </p>
            <div className="flex items-center space-x-6 text-sm text-slate-400">
              <span>🇪🇸 España</span>
              <span>📧 <EMAIL></span>
              <span>📞 +34 123 456 789</span>
            </div>
          </motion.div>
        </div>
      </motion.footer>
    </div>
  )
}
