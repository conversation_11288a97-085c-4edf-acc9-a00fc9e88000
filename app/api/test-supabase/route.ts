import { NextResponse } from 'next/server'
import { testSupabaseConnection, saveContact } from '@/lib/supabase-database'

export async function GET() {
  try {
    const connectionTest = await testSupabaseConnection()
    
    return NextResponse.json({
      success: connectionTest.success,
      message: connectionTest.message,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: `Error: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

export async function POST() {
  try {
    // Datos de prueba para el formulario
    const testData = {
      name: 'Prueba Supabase',
      email: '<EMAIL>',
      phone: '***********',
      company: 'Karedesk Test',
      service: 'Prueba - Migración Supabase',
      message: 'Este es un mensaje de prueba para verificar que Supabase funciona correctamente con el formulario de contacto adaptado para España.',
      budget: '1000-5000',
      timeline: 'flexible',
      priority: 'normal'
    }

    // Intentar guardar el contacto de prueba
    const savedContact = await saveContact(testData)

    return NextResponse.json({
      success: true,
      message: 'Contacto de prueba guardado exitosamente en Supabase',
      contact: {
        id: savedContact.id,
        name: savedContact.name,
        email: savedContact.email,
        service: savedContact.service,
        created_at: savedContact.created_at
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: `Error guardando contacto: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
