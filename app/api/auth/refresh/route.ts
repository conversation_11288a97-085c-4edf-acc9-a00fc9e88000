import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const refreshToken = request.cookies.get('sb-refresh-token')
    
    if (!refreshToken?.value) {
      return NextResponse.json(
        { error: 'No refresh token found', authenticated: false },
        { status: 401 }
      )
    }

    // Renovar la sesión usando el refresh token
    const { data, error } = await supabaseAdmin.auth.refreshSession({
      refresh_token: refreshToken.value
    })

    if (error || !data.session) {
      console.error('Refresh token error:', error)
      
      // Limpiar cookies inválidas
      const response = NextResponse.json(
        { error: 'Invalid refresh token', authenticated: false },
        { status: 401 }
      )

      response.cookies.set('sb-access-token', '', {
        httpOnly: false,
        secure: true,
        sameSite: 'lax',
        path: '/',
        maxAge: 0,
      })

      response.cookies.set('sb-refresh-token', '', {
        httpOnly: false,
        secure: true,
        sameSite: 'lax',
        path: '/',
        maxAge: 0,
      })

      return response
    }

    // Crear respuesta con nuevos tokens
    const response = NextResponse.json({
      authenticated: true,
      user: {
        id: data.user.id,
        email: data.user.email,
        role: data.user.app_metadata?.role || 'user'
      },
      message: 'Token refreshed successfully'
    })

    // Establecer nuevas cookies
    response.cookies.set('sb-access-token', data.session.access_token, {
      httpOnly: false, // Temporalmente false para debugging
      secure: true,
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 7, // 7 días
    })

    response.cookies.set('sb-refresh-token', data.session.refresh_token, {
      httpOnly: false, // Temporalmente false para debugging
      secure: true,
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 días
    })

    return response

  } catch (error) {
    console.error('Refresh API error:', error)
    return NextResponse.json(
      { error: `Server error: ${error instanceof Error ? error.message : 'Unknown error'}`, authenticated: false },
      { status: 500 }
    )
  }
}
