import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const accessToken = request.cookies.get('sb-access-token')
    
    if (!accessToken?.value) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      )
    }

    // Verificar el token y obtener el usuario actual
    const { data: { user: currentUser }, error: verifyError } = await supabaseAdmin.auth.getUser(accessToken.value)
    
    if (verifyError || !currentUser) {
      return NextResponse.json(
        { error: 'Token inválido' },
        { status: 401 }
      )
    }

    const { email, currentPassword, newPassword } = await request.json()

    if (!email || !currentPassword) {
      return NextResponse.json(
        { error: 'Email y contraseña actual son requeridos' },
        { status: 400 }
      )
    }

    // Verificar la contraseña actual
    const { error: signInError } = await supabaseAdmin.auth.signInWithPassword({
      email: currentUser.email!,
      password: currentPassword,
    })

    if (signInError) {
      return NextResponse.json(
        { error: 'Contraseña actual incorrecta' },
        { status: 400 }
      )
    }

    // Preparar los datos de actualización
    const updateData: any = {}
    
    // Actualizar email si cambió
    if (email !== currentUser.email) {
      updateData.email = email
    }

    // Actualizar contraseña si se proporcionó una nueva
    if (newPassword && newPassword.trim() !== '') {
      if (newPassword.length < 6) {
        return NextResponse.json(
          { error: 'La nueva contraseña debe tener al menos 6 caracteres' },
          { status: 400 }
        )
      }
      updateData.password = newPassword
    }

    // Si no hay cambios, devolver éxito
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No hay cambios para actualizar'
      })
    }

    // Actualizar el usuario
    const { data: updatedUser, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
      currentUser.id,
      updateData
    )

    if (updateError) {
      console.error('Error updating user:', updateError)
      return NextResponse.json(
        { error: `Error al actualizar: ${updateError.message}` },
        { status: 400 }
      )
    }

    // Si se cambió el email, el usuario necesitará confirmar el nuevo email
    let message = 'Perfil actualizado correctamente'
    if (updateData.email) {
      message += '. Se ha enviado un email de confirmación a la nueva dirección.'
    }

    return NextResponse.json({
      success: true,
      message,
      user: {
        id: updatedUser.user.id,
        email: updatedUser.user.email,
        role: updatedUser.user.app_metadata?.role || 'user'
      }
    })

  } catch (error) {
    console.error('Update profile API error:', error)
    return NextResponse.json(
      { error: `Error del servidor: ${error instanceof Error ? error.message : 'Error desconocido'}` },
      { status: 500 }
    )
  }
}
