import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email y password son requeridos' },
        { status: 400 }
      )
    }

    // Intentar login con Supabase
    const { data, error } = await supabaseAdmin.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      console.error('Supabase login error:', error)
      return NextResponse.json(
        { error: `Error de autenticación: ${error.message}` },
        { status: 401 }
      )
    }

    if (!data.user || !data.session) {
      return NextResponse.json(
        { error: 'No se pudo crear la sesión' },
        { status: 401 }
      )
    }

    // Crear respuesta con cookies de sesión
    const response = NextResponse.json({
      success: true,
      user: {
        id: data.user.id,
        email: data.user.email,
      },
      message: 'Login exitoso'
    })

    // Establecer cookies de sesión (temporalmente sin httpOnly para debugging)
    response.cookies.set('sb-access-token', data.session.access_token, {
      httpOnly: false, // Temporalmente false para debugging
      secure: true, // Solo HTTPS
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 7, // 7 días
    })

    response.cookies.set('sb-refresh-token', data.session.refresh_token, {
      httpOnly: false, // Temporalmente false para debugging
      secure: true, // Solo HTTPS
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 días
    })

    return response

  } catch (error) {
    console.error('Login API error:', error)
    return NextResponse.json(
      { error: `Error del servidor: ${error instanceof Error ? error.message : 'Error desconocido'}` },
      { status: 500 }
    )
  }
}
