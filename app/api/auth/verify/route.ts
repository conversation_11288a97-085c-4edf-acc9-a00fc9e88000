import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Obtener token de las cookies
    const token = request.cookies.get('sb-access-token')?.value
    
    if (!token) {
      return NextResponse.json(
        { error: 'No token provided', authenticated: false },
        { status: 401 }
      )
    }

    // Crear cliente de Supabase con el token
    const supabase = createServerSupabaseClient()
    
    // Verificar el token con Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      console.error('Token verification failed:', error)
      return NextResponse.json(
        { error: 'Invalid token', authenticated: false },
        { status: 401 }
      )
    }

    // Token válido, devolver información del usuario
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.app_metadata?.role || 'user'
      }
    })

  } catch (error) {
    console.error('Session verification error:', error)
    return NextResponse.json(
      { error: 'Internal server error', authenticated: false },
      { status: 500 }
    )
  }
}
