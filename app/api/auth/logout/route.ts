import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Obtener token de las cookies
    const token = request.cookies.get('sb-access-token')?.value
    
    if (token) {
      // Crear cliente de Supabase y cerrar sesión
      const supabase = createServerSupabaseClient()
      await supabase.auth.signOut()
    }

    // Crear respuesta y limpiar cookies
    const response = NextResponse.json({ 
      success: true, 
      message: 'Logout successful' 
    })

    // Limpiar todas las cookies de autenticación
    response.cookies.set('sb-access-token', '', {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      path: '/',
      maxAge: 0, // Expira inmediatamente
    })

    response.cookies.set('sb-refresh-token', '', {
      httpOnly: true,
      secure: true,
      sameSite: 'lax',
      path: '/',
      maxAge: 0, // Expira inmediatamente
    })

    return response

  } catch (error) {
    console.error('Logout error:', error)
    return NextResponse.json(
      { error: 'Logout failed' },
      { status: 500 }
    )
  }
}
