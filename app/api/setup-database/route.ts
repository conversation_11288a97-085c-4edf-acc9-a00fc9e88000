import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST() {
  try {
    console.log('Verificando configuración de base de datos...')

    // Verificar si la tabla contacts existe
    const { data, error: selectError } = await supabaseAdmin
      .from('contacts')
      .select('id')
      .limit(1)

    if (selectError) {
      console.error('Error verificando tabla contacts:', selectError)
      return NextResponse.json({
        success: false,
        message: `Error: La tabla contacts no existe en Supabase. ${selectError.message}`,
        solution: 'Necesitas ejecutar el script SQL de configuración',
        scriptLocation: 'scripts/supabase-setup.sql',
        instructions: [
          '1. Ve a tu dashboard de Supabase',
          '2. Navega a SQL Editor', 
          '3. Copia y pega el contenido del archivo scripts/supabase-setup.sql',
          '4. Ejecuta el script',
          '5. Vuelve a probar este endpoint'
        ]
      }, { status: 500 })
    }

    // Si la tabla existe, insertar un dato de prueba simple
    const { error: insertError } = await supabaseAdmin
      .from('contacts')
      .insert({
        name: 'Usuario de Prueba Supabase',
        email: `prueba-supabase-${Date.now()}@karedesk.es`,
        phone: '+34 ***********',
        company: 'Karedesk España Test',
        service: 'Configuración Supabase',
        message: 'Este es un contacto de prueba creado automáticamente durante la configuración de Supabase.',
        budget: '1000-5000',
        timeline: 'flexible',
        priority: 'normal'
      })

    if (insertError) {
      console.error('Error insertando datos de prueba:', insertError)
      return NextResponse.json({
        success: false,
        message: `Error insertando datos de prueba: ${insertError.message}`,
        suggestion: 'Verifica la estructura de la tabla en Supabase'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Base de datos configurada exitosamente',
      details: {
        tableExists: 'Tabla contacts encontrada',
        testData: 'Datos de prueba insertados correctamente'
      }
    })

  } catch (error) {
    console.error('Error configurando base de datos:', error)
    return NextResponse.json({
      success: false,
      message: `Error: ${error instanceof Error ? error.message : 'Error desconocido'}`
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Usa POST para verificar la configuración de la base de datos',
    instructions: 'Ejecuta: curl -X POST http://localhost:3000/api/setup-database',
    scriptLocation: 'scripts/supabase-setup.sql'
  })
}