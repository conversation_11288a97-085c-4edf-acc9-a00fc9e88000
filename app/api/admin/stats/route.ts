import { NextResponse } from "next/server"
import { getContactStats } from "@/lib/supabase-database"

export async function GET() {
  try {
    const stats = await getContactStats()

    return NextResponse.json({
      success: true,
      data: {
        total: stats.total,
        thisMonth: stats.thisMonth,
        thisWeek: stats.thisWeek,
        byService: stats.byService,
        // Valores por defecto para compatibilidad con el frontend
        new: 0,
        in_progress: 0,
        closed: 0,
        response_rate: 0,
        avg_response_time: 0,
        needing_followup: 0,
        followup_contacts: []
      }
    })

  } catch (error) {
    console.error("Error fetching stats:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
