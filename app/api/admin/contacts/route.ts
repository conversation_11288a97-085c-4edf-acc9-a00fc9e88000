import { NextRequest, NextResponse } from "next/server"
import { getAllContacts, updateContact } from "@/lib/supabase-database"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0

    const result = await getAllContacts(limit, offset)
    
    return NextResponse.json({
      success: true,
      data: result.contacts,
      total: result.total,
      count: result.contacts.length
    })

  } catch (error) {
    console.error("Error fetching contacts:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { contactId, ...updates } = body

    if (!contactId) {
      return NextResponse.json(
        { success: false, error: "contactId es requerido" },
        { status: 400 }
      )
    }

    // Actualizar el contacto en Supabase
    const updatedContact = await updateContact(contactId, updates)

    return NextResponse.json({
      success: true,
      message: "Contacto actualizado exitosamente",
      data: updatedContact
    })

  } catch (error) {
    console.error("Error updating contact:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
