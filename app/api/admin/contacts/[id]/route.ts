import { NextRequest, NextResponse } from "next/server"
import { getContactById } from "@/lib/supabase-database"

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const contactId = parseInt(id)
    
    if (isNaN(contactId)) {
      return NextResponse.json(
        { success: false, error: "ID de contacto inválido" },
        { status: 400 }
      )
    }

    const contact = await getContactById(contactId)
    
    if (!contact) {
      return NextResponse.json(
        { success: false, error: "Contacto no encontrado" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        contact: contact,
        activities: [] // Por ahora no hay sistema de actividades en Supabase
      }
    })

  } catch (error) {
    console.error("Error fetching contact:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const contactId = parseInt(id)
    const body = await request.json()
    const { activity_type, description, performed_by } = body

    if (isNaN(contactId)) {
      return NextResponse.json(
        { success: false, error: "ID de contacto inválido" },
        { status: 400 }
      )
    }

    if (!activity_type || !description) {
      return NextResponse.json(
        { success: false, error: "activity_type y description son requeridos" },
        { status: 400 }
      )
    }

    // Por ahora retornamos éxito sin implementar actividades
    // TODO: Implementar sistema de actividades en Supabase
    console.log("Activity would be added:", { contactId, activity_type, description, performed_by })

    return NextResponse.json({
      success: true,
      message: "Funcionalidad de actividades pendiente de implementación"
    })

  } catch (error) {
    console.error("Error adding activity:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
