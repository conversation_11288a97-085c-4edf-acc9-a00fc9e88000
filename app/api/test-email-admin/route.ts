import { NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"

const resend = new Resend(process.env.RESEND_API_KEY)

export async function GET() {
  try {
    console.log("🧪 Iniciando test de email al administrador...")
    console.log("📧 Email destino:", process.env.ADMIN_EMAIL)
    console.log("🔑 API Key configurada:", process.env.RESEND_API_KEY ? "✅ Sí" : "❌ No")

    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json(
        { success: false, error: "RESEND_API_KEY no configurada" },
        { status: 500 }
      )
    }

    if (!process.env.ADMIN_EMAIL) {
      return NextResponse.json(
        { success: false, error: "ADMIN_EMAIL no configurada" },
        { status: 500 }
      )
    }

    // Email de prueba al administrador
    const testEmailResult = await resend.emails.send({
      from: "<PERSON><PERSON><PERSON> <<EMAIL>>",
      to: ["<EMAIL>"],
      subject: "🧪 Test de Email - Karedesk Sistema",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Test Email Karedesk</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #3b82f6, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px; }
            .success { background: #10b981; color: white; padding: 15px; border-radius: 8px; margin: 20px 0; }
            .info { background: #3b82f6; color: white; padding: 10px; border-radius: 5px; margin: 10px 0; }
            .footer { text-align: center; margin-top: 30px; color: #64748b; font-size: 14px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🧪 Test de Email Exitoso</h1>
              <p>Sistema de Notificaciones Karedesk</p>
            </div>
            <div class="content">
              <div class="success">
                <strong>✅ ¡Email de prueba enviado correctamente!</strong>
              </div>
              
              <h2>📋 Información del Test</h2>
              <div class="info">
                <strong>📧 Email destino:</strong> <EMAIL>
              </div>
              <div class="info">
                <strong>🕐 Fecha y hora:</strong> ${new Date().toLocaleString('es-ES', { timeZone: 'Europe/Madrid' })}
              </div>
              <div class="info">
                <strong>🌐 Servidor:</strong> Karedesk Production
              </div>
              
              <h2>🎯 ¿Qué significa esto?</h2>
              <p>Si recibes este email, significa que:</p>
              <ul>
                <li>✅ La API de Resend está configurada correctamente</li>
                <li>✅ Tu email de administrador está configurado</li>
                <li>✅ Los formularios de contacto te enviarán notificaciones</li>
                <li>✅ El sistema de emails está funcionando perfectamente</li>
              </ul>
              
              <h2>📝 Próximos pasos</h2>
              <p>Ahora cuando alguien complete un formulario de contacto en tu sitio web, recibirás automáticamente una notificación en esta dirección de email con todos los detalles del contacto.</p>
              
              <div class="footer">
                <p>🚀 <strong>Karedesk</strong> - Sistema de Gestión de Contactos</p>
                <p>Este es un email automático del sistema de pruebas</p>
              </div>
            </div>
          </div>
        </body>
        </html>
      `,
    })

    console.log("✅ Email de prueba enviado exitosamente:", testEmailResult.data?.id)

    return NextResponse.json({
      success: true,
      message: "Email de prueba enviado correctamente",
      emailId: testEmailResult.data?.id,
      adminEmail: "<EMAIL>",
      timestamp: new Date().toISOString(),
    })

  } catch (error) {
    console.error("❌ Error enviando email de prueba:", error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        adminEmail: "<EMAIL>",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

export async function POST() {
  // Permitir tanto GET como POST para flexibilidad
  return GET()
}
