import { NextRequest, NextResponse } from "next/server"
import { submitContactForm, type ContactFormData } from "@/lib/contact-actions"

export async function POST(request: NextRequest) {
  try {
    console.log("=== API ROUTE /api/contact ===")
    
    // Verificar Content-Type
    const contentType = request.headers.get('content-type')
    console.log("Content-Type:", contentType)
    
    let data: ContactFormData
    
    if (contentType?.includes('application/json')) {
      // Datos JSON
      data = await request.json()
      console.log("Datos JSON recibidos:", data)
    } else if (contentType?.includes('multipart/form-data')) {
      // FormData
      const formData = await request.formData()
      console.log("FormData recibido")
      
      data = {
        name: formData.get("name") as string,
        email: formData.get("email") as string,
        phone: formData.get("phone") as string,
        company: formData.get("company") as string,
        service: formData.get("service") as string,
        message: formData.get("message") as string,
        budget: formData.get("budget") as string,
        timeline: formData.get("timeline") as string,
      }
      
      console.log("Datos extraídos de FormData:", data)
    } else {
      return NextResponse.json(
        { success: false, error: "Content-Type no soportado" },
        { status: 400 }
      )
    }

    // Validar datos básicos
    if (!data.name || !data.email || !data.service) {
      return NextResponse.json(
        { 
          success: false, 
          error: "Faltan campos requeridos: name, email, service" 
        },
        { status: 400 }
      )
    }

    // Procesar el formulario usando la función existente
    console.log("Llamando a submitContactForm...")
    const result = await submitContactForm(data)
    
    console.log("Resultado de submitContactForm:", result)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        contactId: result.contactId,
        emailSent: result.emailSent
      })
    } else {
      return NextResponse.json(
        {
          success: false,
          error: result.message,
          errors: result.errors
        },
        { status: 400 }
      )
    }

  } catch (error) {
    console.error("Error en API route /api/contact:", error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error interno del servidor",
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Método GET para testing
export async function GET() {
  return NextResponse.json({
    message: "API route /api/contact funcionando",
    methods: ["POST"],
    timestamp: new Date().toISOString()
  })
}
