import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST() {
  try {
    // Crear usuario administrador
    const { data, error } = await supabaseAdmin.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'admin123',
      email_confirm: true,
      user_metadata: {
        role: 'admin',
        name: 'Administra<PERSON> Kared<PERSON>'
      }
    })

    if (error) {
      return NextResponse.json({
        success: false,
        message: `Error creando usuario: ${error.message}`,
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Usuario administrador creado exitosamente',
      user: {
        id: data.user.id,
        email: data.user.email,
        created_at: data.user.created_at
      },
      credentials: {
        email: '<EMAIL>',
        password: 'admin123'
      },
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: `Error: ${error instanceof Error ? error.message : 'Error desconocido'}`,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
