import { NextRequest, NextResponse } from "next/server"
import { submitContactFormWithFormData, type ContactFormState } from "@/lib/contact-actions"

async function handleTestContact() {
  try {
    // Datos de prueba
    const testData = {
      name: "Cliente Test Karedesk",
      email: "<EMAIL>", // Email especial de Resend para testing
      phone: "+34612345678",
      company: "Empresa Test SL",
      service: "Consultoría IA",
      message: "Este es un mensaje de prueba para verificar que el sistema de notificaciones por email funciona correctamente. Estamos interesados en implementar soluciones de inteligencia artificial en nuestra empresa.",
      budget: "10000-25000",
      timeline: "2-3-meses"
    }

    // Crear FormData
    const formData = new FormData()
    Object.entries(testData).forEach(([key, value]) => {
      formData.append(key, value)
    })

    // Estado inicial
    const initialState: ContactFormState = {}

    // Llamar a la función de submit
    const result = await submitContactFormWithFormData(initialState, formData)

    return NextResponse.json({
      success: true,
      message: "Test de formulario de contacto ejecutado",
      result: result,
      testData: testData
    })

  } catch (error) {
    console.error("Error en test de contacto:", error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return handleTestContact()
}

export async function POST(request: NextRequest) {
  return handleTestContact()
}
