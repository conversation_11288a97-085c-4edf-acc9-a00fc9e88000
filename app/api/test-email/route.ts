import { NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"

const resend = new Resend(process.env.RESEND_API_KEY)

export async function GET() {
  try {
    // Verificar que la API key esté configurada
    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json(
        {
          success: false,
          error: "RESEND_API_KEY no está configurada"
        },
        { status: 500 }
      )
    }

    // Enviar email de prueba
    const result = await resend.emails.send({
      from: "Karedesk Test <<EMAIL>>",
      to: ["<EMAIL>"], // Email especial de Resend para testing
      subject: "🧪 Test de configuración - Karedesk",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #14b8a6;">✅ Test de Email Exitoso</h1>
          <p>Este es un email de prueba para verificar que la configuración de Resend está funcionando correctamente.</p>
          <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>Detalles de la configuración:</h3>
            <ul>
              <li><strong>API Key:</strong> Configurada ✅</li>
              <li><strong>Servicio:</strong> Resend</li>
              <li><strong>Proyecto:</strong> Karedesk Website</li>
              <li><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</li>
            </ul>
          </div>
          <p style="color: #666;">Si recibes este email, significa que el sistema de emails está funcionando correctamente.</p>
        </div>
      `,
    })

    return NextResponse.json({
      success: true,
      message: "Email de prueba enviado exitosamente",
      emailId: result.data?.id,
      details: {
        apiKeyConfigured: !!process.env.RESEND_API_KEY,
        timestamp: new Date().toISOString(),
      }
    })

  } catch (error) {
    console.error("Error en test de email:", error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        details: {
          apiKeyConfigured: !!process.env.RESEND_API_KEY,
          timestamp: new Date().toISOString(),
        }
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verificar autenticación
    const accessToken = request.cookies.get('sb-access-token')

    if (!accessToken?.value) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      )
    }

    const { to, subject, message } = await request.json()

    if (!to || !subject || !message) {
      return NextResponse.json(
        { error: 'Faltan parámetros requeridos: to, subject, message' },
        { status: 400 }
      )
    }

    // Enviar email de prueba personalizado
    const { data, error } = await resend.emails.send({
      from: 'Karedesk España <<EMAIL>>',
      to: [to],
      subject: subject,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; border-radius: 10px 10px 0 0; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🧪 Email de Prueba</h1>
            <p style="color: #e0e7ff; margin: 10px 0 0 0;">Configuración del Sistema Karedesk</p>
          </div>

          <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e5e7eb;">
            <h2 style="color: #374151; margin-top: 0;">✅ Configuración de Email Funcionando</h2>

            <p style="color: #6b7280; line-height: 1.6;">
              Este es un email de prueba enviado desde la configuración del sistema Karedesk España.
            </p>

            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <p style="margin: 0; color: #374151;"><strong>Mensaje:</strong></p>
              <p style="margin: 10px 0 0 0; color: #6b7280;">${message}</p>
            </div>

            <div style="border-top: 1px solid #e5e7eb; padding-top: 20px; margin-top: 30px;">
              <p style="color: #9ca3af; font-size: 14px; margin: 0;">
                📧 Enviado desde: <strong>Karedesk España</strong><br>
                🕒 Fecha: <strong>${new Date().toLocaleString('es-ES')}</strong><br>
                🔧 Proveedor: <strong>Resend</strong>
              </p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px;">
            <p style="color: #9ca3af; font-size: 12px;">
              Este es un email automático de prueba. No es necesario responder.
            </p>
          </div>
        </div>
      `,
    })

    if (error) {
      console.error('Resend error:', error)
      return NextResponse.json(
        { error: `Error al enviar email: ${error.message}` },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Email de prueba enviado correctamente',
      emailId: data?.id
    })

  } catch (error) {
    console.error('Test email API error:', error)
    return NextResponse.json(
      { error: `Error del servidor: ${error instanceof Error ? error.message : 'Error desconocido'}` },
      { status: 500 }
    )
  }
}
