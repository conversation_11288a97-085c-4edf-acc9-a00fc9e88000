"use client"

import { useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { AlertTriangle, RefreshCw, Home } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log del error para debugging
    console.error("Error en la aplicación:", error)
  }, [error])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center px-4 font-inter">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center max-w-2xl mx-auto"
      >
        <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
          <CardContent className="p-12">
            {/* Logo */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Image src="/logo.png" alt="Karedesk" width={80} height={80} className="w-20 h-20 mx-auto mb-4" />
            </motion.div>

            {/* Error Icon */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mb-6"
            >
              <div className="w-24 h-24 bg-red-600/20 rounded-3xl flex items-center justify-center mx-auto mb-6">
                <AlertTriangle className="w-12 h-12 text-red-400" />
              </div>
              <h1 className="text-3xl font-bold text-white mb-4 tracking-tight">¡Oops! Algo salió mal</h1>
              <p className="text-xl text-slate-400 font-light leading-relaxed">
                Ha ocurrido un error inesperado. Nuestro equipo ha sido notificado y está trabajando en solucionarlo.
              </p>
            </motion.div>

            {/* Error Details (solo en desarrollo) */}
            {process.env.NODE_ENV === "development" && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="mb-6 p-4 bg-slate-800/50 rounded-2xl text-left"
              >
                <p className="text-red-400 text-sm font-mono break-all">{error.message}</p>
                {error.digest && <p className="text-slate-500 text-xs mt-2">Error ID: {error.digest}</p>}
              </motion.div>
            )}

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                onClick={reset}
                className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-2xl font-semibold"
              >
                <RefreshCw className="w-5 h-5 mr-2" />
                Intentar de nuevo
              </Button>
              <Link href="/">
                <Button
                  variant="outline"
                  className="border-slate-600 text-white hover:bg-slate-800 px-8 py-3 rounded-2xl font-semibold"
                >
                  <Home className="w-5 h-5 mr-2" />
                  Ir al inicio
                </Button>
              </Link>
            </motion.div>

            {/* Contact Info */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mt-8 pt-8 border-t border-slate-700"
            >
              <p className="text-slate-400 mb-4 font-medium">¿Necesitas ayuda inmediata?</p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <a href="mailto:<EMAIL>" className="text-teal-400 hover:text-teal-300 transition-colors">
                  <EMAIL>
                </a>
                <span className="hidden sm:inline text-slate-600">|</span>
                <a href="tel:+15551234567" className="text-teal-400 hover:text-teal-300 transition-colors">
                  +****************
                </a>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
