"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Home, ArrowLeft } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center px-4 font-inter">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center max-w-2xl mx-auto"
      >
        <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
          <CardContent className="p-12">
            {/* Logo */}
            <motion.div
              className="mb-8"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Image src="/logo.png" alt="Karedesk" width={80} height={80} className="w-20 h-20 mx-auto mb-4" />
            </motion.div>

            {/* Error Code */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="mb-6"
            >
              <h1 className="text-8xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-teal-400 to-emerald-400 mb-4">
                404
              </h1>
              <h2 className="text-3xl font-bold text-white mb-4 tracking-tight">Página no encontrada</h2>
              <p className="text-xl text-slate-400 font-light leading-relaxed">
                Lo sentimos, la página que buscas no existe o ha sido movida.
              </p>
            </motion.div>

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Link href="/">
                <Button className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-2xl font-semibold">
                  <Home className="w-5 h-5 mr-2" />
                  Ir al inicio
                </Button>
              </Link>
              <Button
                variant="outline"
                onClick={() => window.history.back()}
                className="border-slate-600 text-white hover:bg-slate-800 px-8 py-3 rounded-2xl font-semibold"
              >
                <ArrowLeft className="w-5 h-5 mr-2" />
                Volver atrás
              </Button>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="mt-8 pt-8 border-t border-slate-700"
            >
              <p className="text-slate-400 mb-4 font-medium">¿Buscas algo específico?</p>
              <div className="flex flex-wrap justify-center gap-3">
                <Link href="/#servicios">
                  <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white rounded-xl">
                    Servicios
                  </Button>
                </Link>
                <Link href="/#nosotros">
                  <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white rounded-xl">
                    Nosotros
                  </Button>
                </Link>
                <Link href="/#contacto">
                  <Button variant="ghost" size="sm" className="text-slate-300 hover:text-white rounded-xl">
                    Contacto
                  </Button>
                </Link>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
