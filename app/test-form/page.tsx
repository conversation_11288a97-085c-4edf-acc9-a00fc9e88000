"use client"

import { motion } from "framer-motion"
import SimpleContactForm from "@/components/simple-contact-form"
import ContactForm from "@/components/contact-form"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, TestTube, Sparkles } from "lucide-react"
import Link from "next/link"

export default function TestFormPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {/* Header */}
      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-4 mb-8"
        >
          <Link 
            href="/"
            className="flex items-center gap-2 text-slate-400 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            Volver al inicio
          </Link>
          <Badge variant="outline" className="flex items-center gap-2">
            <TestTube className="w-3 h-3" />
            Página de Pruebas
          </Badge>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
            Formularios de Contacto
          </h1>
          <p className="text-xl text-slate-300 max-w-2xl mx-auto">
            Comparación entre el formulario original y el formulario mejorado con validación en tiempo real
          </p>
        </motion.div>
      </div>

      {/* Forms Comparison */}
      <div className="container mx-auto px-4 pb-20">
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Enhanced Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-6">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-white flex items-center justify-center gap-2">
                  <Sparkles className="w-6 h-6 text-yellow-500" />
                  Formulario Mejorado
                </CardTitle>
                <div className="flex flex-wrap justify-center gap-2 mt-4">
                  <Badge variant="outline" className="text-xs">Validación en tiempo real</Badge>
                  <Badge variant="outline" className="text-xs">Formateo automático</Badge>
                  <Badge variant="outline" className="text-xs">Animaciones fluidas</Badge>
                  <Badge variant="outline" className="text-xs">Notificaciones</Badge>
                </div>
              </CardHeader>
            </Card>
            
            <SimpleContactForm
              service="Prueba - Formulario España"
              title="¡Formulario optimizado para España!"
              description="Teléfono opcional, formato español, euros"
              showBudget={true}
              showTimeline={true}
            />
          </motion.div>

          {/* Original Form */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm mb-6">
              <CardHeader className="text-center">
                <CardTitle className="text-2xl font-bold text-white flex items-center justify-center gap-2">
                  📝 Formulario Original
                </CardTitle>
                <div className="flex flex-wrap justify-center gap-2 mt-4">
                  <Badge variant="outline" className="text-xs">Validación al enviar</Badge>
                  <Badge variant="outline" className="text-xs">Funcional básico</Badge>
                </div>
              </CardHeader>
            </Card>
            
            <ContactForm
              service="Prueba - Formulario Original España"
              title="Formulario actual"
              description="Actualizado para España - Teléfono opcional"
              showBudget={true}
              showTimeline={true}
            />
          </motion.div>
        </div>

        {/* Features Comparison */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-20"
        >
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-2xl font-bold text-white text-center">
                Comparación de Características
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-green-400 mb-4">✨ Formulario Mejorado - España</h3>
                  <ul className="space-y-2 text-slate-300">
                    <li>✅ Validación en tiempo real</li>
                    <li>✅ Formateo automático teléfono español</li>
                    <li>✅ Teléfono opcional (no obligatorio)</li>
                    <li>✅ Indicadores visuales de estado</li>
                    <li>✅ Animaciones fluidas</li>
                    <li>✅ Notificaciones toast</li>
                    <li>✅ Contador de caracteres</li>
                    <li>✅ Mejor accesibilidad</li>
                    <li>✅ Componentes reutilizables</li>
                    <li>✅ Hook personalizado</li>
                    <li>✅ Mejor manejo de errores</li>
                    <li>✅ Adaptado para España</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-blue-400 mb-4">📝 Formulario Original - España</h3>
                  <ul className="space-y-2 text-slate-300">
                    <li>✅ Funcionalidad básica</li>
                    <li>✅ Envío de emails</li>
                    <li>✅ Guardado en base de datos</li>
                    <li>✅ Validación del servidor</li>
                    <li>✅ Teléfono opcional</li>
                    <li>✅ Adaptado para España</li>
                    <li>❌ Sin validación en tiempo real</li>
                    <li>❌ Sin formateo automático</li>
                    <li>❌ Feedback limitado</li>
                    <li>❌ Sin notificaciones</li>
                    <li>❌ Código menos modular</li>
                    <li>❌ UX básica</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
