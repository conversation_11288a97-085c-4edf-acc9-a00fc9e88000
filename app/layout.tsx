import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
})

export const metadata: Metadata = {
  title: {
    default: "Karedesk - Soluciones Digitales del Futuro",
    template: "%s | Karedesk",
  },
  description:
    "Transformamos tu negocio con tecnología de vanguardia. Especialistas en seguridad web, inteligencia artificial, desarrollo digital y soporte técnico.",
  keywords: [
    "desarrollo web",
    "seguridad informática",
    "inteligencia artificial",
    "soporte técnico",
    "consultoría IT",
    "análisis vulnerabilidades",
    "páginas web",
    "transformación digital",
  ],
  authors: [{ name: "Karedesk" }],
  creator: "Karedes<PERSON>",
  publisher: "Karedesk",
  metadataBase: new URL("https://karedesk.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "es_ES",
    url: "https://karedesk.com",
    siteName: "Karedesk",
    title: "Karedesk - Soluciones Digitales del Futuro",
    description:
      "Transformamos tu negocio con tecnología de vanguardia. Especialistas en seguridad web, inteligencia artificial y desarrollo digital.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Karedesk - Soluciones Digitales",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Karedesk - Soluciones Digitales del Futuro",
    description: "Transformamos tu negocio con tecnología de vanguardia.",
    images: ["/og-image.jpg"],
    creator: "@karedesk",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "google-site-verification-code", // Agregar código real
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="es" className={inter.variable}>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#0f172a" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${inter.className} antialiased`}>{children}</body>
    </html>
  )
}
