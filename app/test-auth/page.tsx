"use client"

import { useState, useEffect } from 'react'
import { createClientSupabaseClient } from '@/lib/supabase-client'

export default function TestAuthPage() {
  const [status, setStatus] = useState('Inicializando...')
  const [user, setUser] = useState<any>(null)
  const [error, setError] = useState('')

  useEffect(() => {
    const testSupabase = async () => {
      try {
        const supabase = createClientSupabaseClient()
        setStatus('Cliente Supabase creado')

        // Verificar conexión
        const { data, error } = await supabase.auth.getUser()
        
        if (error) {
          setError(`Error de autenticación: ${error.message}`)
        } else {
          setStatus('Conexión exitosa')
          setUser(data.user)
        }
      } catch (err) {
        setError(`Error: ${err instanceof Error ? err.message : 'Error desconocido'}`)
      }
    }

    testSupabase()
  }, [])

  const handleTestLogin = async () => {
    try {
      const supabase = createClientSupabaseClient()
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'admin123',
      })

      if (error) {
        setError(`Error de login: ${error.message}`)
      } else {
        setStatus('Login exitoso')
        setUser(data.user)
      }
    } catch (err) {
      setError(`Error: ${err instanceof Error ? err.message : 'Error desconocido'}`)
    }
  }

  return (
    <div className="min-h-screen bg-slate-100 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Test de Autenticación Supabase</h1>
        
        <div className="bg-white rounded-lg shadow p-6 space-y-4">
          <div>
            <h2 className="text-xl font-semibold mb-2">Estado</h2>
            <p className="text-slate-600">{status}</p>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>Error:</strong> {error}
            </div>
          )}

          {user && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              <strong>Usuario autenticado:</strong>
              <pre className="mt-2 text-sm">{JSON.stringify(user, null, 2)}</pre>
            </div>
          )}

          <div className="space-y-2">
            <button
              onClick={handleTestLogin}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Probar Login
            </button>
          </div>

          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">Variables de Entorno</h3>
            <div className="text-sm space-y-1">
              <p><strong>SUPABASE_URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
              <p><strong>ANON_KEY:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Configurada' : 'No configurada'}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
