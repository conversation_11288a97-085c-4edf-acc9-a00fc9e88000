{"name": "karedesk-website", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:manual": "node test-final-deployment.js", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next node_modules/.cache", "prepare": "husky install"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@neondatabase/serverless": "latest", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "latest", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19", "react-dom": "^19", "resend": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "latest"}, "devDependencies": {"@next/bundle-analyzer": "^15.2.4", "@playwright/test": "^1.48.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.0", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-next": "15.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.10", "postcss": "^8.5", "prettier": "^3.4.2", "tailwindcss": "^3.4.0", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}}