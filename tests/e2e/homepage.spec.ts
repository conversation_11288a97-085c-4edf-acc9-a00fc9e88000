import { test, expect } from '@playwright/test'

test.describe('Homepage', () => {
  test('should load homepage successfully', async ({ page }) => {
    await page.goto('/')
    
    // Check if the page loads
    await expect(page).toHaveTitle(/Karedesk/)
    
    // Check main navigation
    await expect(page.locator('nav')).toBeVisible()
    
    // Check hero section
    await expect(page.locator('h1')).toContainText('Soluciones Digitales')
    
    // Check services section
    await expect(page.locator('#servicios')).toBeVisible()
    
    // Check contact section
    await expect(page.locator('#contacto')).toBeVisible()
  })

  test('should navigate to service pages', async ({ page }) => {
    await page.goto('/')
    
    // Click on a service link
    await page.click('text=Consultoría IA')
    
    // Should navigate to service page
    await expect(page).toHaveURL(/\/servicios\/consultoria-ia/)
    await expect(page.locator('h1')).toContainText('Consultoría IA')
  })

  test('should open contact form', async ({ page }) => {
    await page.goto('/')
    
    // Click contact button
    await page.click('text=Contactar')
    
    // Contact form should be visible
    await expect(page.locator('form')).toBeVisible()
    await expect(page.locator('input[name="name"]')).toBeVisible()
    await expect(page.locator('input[name="email"]')).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/')
    
    // Check mobile navigation
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible()
    
    // Check content is still visible
    await expect(page.locator('h1')).toBeVisible()
  })
})
