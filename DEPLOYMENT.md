# 🚀 Guía de Despliegue - Karedesk Website

## 📋 Pre-requisitos

### 1. Cuentas necesarias
- ✅ **Vercel Account** - [vercel.com](https://vercel.com)
- ✅ **Neon Database** - [neon.tech](https://neon.tech) 
- ✅ **Resend Account** - [resend.com](https://resend.com)
- ✅ **GitHub Account** - [github.com](https://github.com)

### 2. Configuración de servicios

#### 🗄️ Neon Database
1. Crear proyecto en Neon
2. Obtener `DATABASE_URL` y `DATABASE_URL_UNPOOLED`
3. Ejecutar scripts SQL:
   - `scripts/001-create-database.sql`
   - `scripts/002-seed-sample-data.sql`

#### 📧 Resend Email Service
1. Crear cuenta en Resend
2. Verificar dominio (opcional pero recomendado)
3. Obtener API Key (`RESEND_API_KEY`)
4. Configurar emails de envío

## 🚀 Despliegue en Vercel

### Opción 1: Deploy But<PERSON> (Recomendado)
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/tu-usuario/karedesk-website)

### Opción 2: Manual
1. **Fork/Clone del repositorio**
   \`\`\`bash
   git clone https://github.com/tu-usuario/karedesk-website.git
   cd karedesk-website
   \`\`\`

2. **Conectar con Vercel**
   \`\`\`bash
   npx vercel
   \`\`\`

3. **Configurar variables de entorno en Vercel**
   - Ve a tu proyecto en Vercel Dashboard
   - Settings → Environment Variables
   - Agregar todas las variables del `.env.example`

### Variables de entorno requeridas:
\`\`\`bash
# Database
DATABASE_URL=postgresql://...
DATABASE_URL_UNPOOLED=postgresql://...

# Email
RESEND_API_KEY=re_...

# Site
NEXT_PUBLIC_SITE_URL=https://tu-dominio.vercel.app
\`\`\`

## 🔧 Configuración Post-Deploy

### 1. Verificar funcionalidad
- ✅ Sitio web carga correctamente
- ✅ Formularios de contacto funcionan
- ✅ Emails se envían correctamente
- ✅ Dashboard admin accesible en `/admin`
- ✅ Base de datos conectada

### 2. Configurar dominio personalizado (Opcional)
1. En Vercel Dashboard → Settings → Domains
2. Agregar tu dominio personalizado
3. Configurar DNS según instrucciones
4. Actualizar `NEXT_PUBLIC_SITE_URL`

### 3. Configurar Resend con dominio personalizado
1. En Resend Dashboard → Domains
2. Agregar tu dominio
3. Configurar registros DNS (SPF, DKIM, DMARC)
4. Verificar dominio
5. Actualizar emails en variables de entorno

## 📊 Monitoreo y Analytics

### 1. Vercel Analytics (Incluido)
- Métricas de rendimiento automáticas
- Core Web Vitals
- Estadísticas de tráfico

### 2. Google Analytics (Opcional)
\`\`\`bash
# Agregar a variables de entorno
NEXT_PUBLIC_GOOGLE_ANALYTICS=G-XXXXXXXXXX
\`\`\`

### 3. Resend Logs
- Dashboard de Resend para métricas de email
- Logs de entrega y apertura
- Estadísticas de bounce/spam

## 🔒 Seguridad

### Headers de seguridad (Ya configurados)
- ✅ X-Frame-Options: DENY
- ✅ X-Content-Type-Options: nosniff
- ✅ Referrer-Policy: strict-origin-when-cross-origin
- ✅ Permissions-Policy configurado

### Protección de rutas
- ✅ `/admin` sin indexación en robots.txt
- ✅ `/api` protegido
- ✅ Variables de entorno seguras

## 🚀 Optimizaciones de Rendimiento

### Ya implementadas:
- ✅ **Next.js 15** con App Router
- ✅ **Compresión** habilitada
- ✅ **Imágenes optimizadas** con next/image
- ✅ **Fonts optimizados** con next/font
- ✅ **Lazy loading** de componentes
- ✅ **Minificación** automática
- ✅ **Tree shaking** de dependencias

### Métricas objetivo:
- 🎯 **Lighthouse Score**: 95+
- 🎯 **First Contentful Paint**: <1.5s
- 🎯 **Largest Contentful Paint**: <2.5s
- 🎯 **Cumulative Layout Shift**: <0.1

## 📱 PWA Features

### Configurado:
- ✅ **Manifest.json** para instalación
- ✅ **Service Worker** (automático con Next.js)
- ✅ **Iconos** para diferentes dispositivos
- ✅ **Theme colors** configurados

## 🔄 CI/CD Pipeline

### Vercel automático:
- ✅ **Deploy en push** a main branch
- ✅ **Preview deploys** para PRs
- ✅ **Rollback** automático en errores
- ✅ **Edge caching** global

## 📞 Soporte Post-Deploy

### Contactos de emergencia:
- 📧 **Email**: <EMAIL>
- 📱 **Teléfono**: +****************
- 🌐 **Dashboard**: https://tu-dominio.vercel.app/admin

### Logs y debugging:
- **Vercel Functions**: Dashboard → Functions → Logs
- **Database**: Neon Dashboard → Monitoring
- **Emails**: Resend Dashboard → Logs

## ✅ Checklist Final

Antes de ir a producción:

- [ ] ✅ Todas las variables de entorno configuradas
- [ ] ✅ Base de datos creada y poblada
- [ ] ✅ Emails de prueba enviados exitosamente
- [ ] ✅ Formularios de contacto probados
- [ ] ✅ Dashboard admin accesible
- [ ] ✅ Dominio personalizado configurado (opcional)
- [ ] ✅ SSL/HTTPS funcionando
- [ ] ✅ Sitemap.xml accesible
- [ ] ✅ Robots.txt configurado
- [ ] ✅ Analytics configurado (opcional)
- [ ] ✅ Monitoreo de errores activo

## 🎉 ¡Listo para producción!

Tu sitio web de Karedesk está ahora desplegado y listo para recibir clientes. 

**URL del sitio**: https://tu-dominio.vercel.app
**Dashboard admin**: https://tu-dominio.vercel.app/admin

¡Felicidades! 🚀
