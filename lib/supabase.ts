import { createClient } from '@supabase/supabase-js'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Cliente para el lado del cliente
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Cliente para el servidor con service role key
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Cliente para el servidor con autenticación de usuario
export const createServerSupabaseClient = () => {
  const cookieStore = cookies()

  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )
}

// Tipos para TypeScript
export interface Contact {
  id: number
  name: string
  email: string
  phone?: string
  company?: string
  service: string
  message: string
  budget?: string
  timeline?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  client_email_id?: string
  internal_email_id?: string
  created_at: string
  updated_at: string
}

export interface ContactFormData {
  name: string
  email: string
  phone?: string
  company?: string
  service: string
  message: string
  budget?: string
  timeline?: string
}
