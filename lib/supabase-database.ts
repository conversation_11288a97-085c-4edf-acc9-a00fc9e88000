import { supabase, supabaseAdmin, type Contact, type ContactFormData } from './supabase'

// Función para guardar un contacto
export async function saveContact(data: ContactFormData & { 
  priority?: string
  client_email_id?: string
  internal_email_id?: string
}): Promise<Contact> {
  try {
    const contactData = {
      name: data.name,
      email: data.email,
      phone: data.phone || null,
      company: data.company || null,
      service: data.service,
      message: data.message,
      budget: data.budget || null,
      timeline: data.timeline || null,
      priority: data.priority || 'normal',
      client_email_id: data.client_email_id || null,
      internal_email_id: data.internal_email_id || null,
    }

    const { data: contact, error } = await supabaseAdmin
      .from('contacts')
      .insert(contactData)
      .select()
      .single()

    if (error) {
      console.error('Error saving contact to Supabase:', error)
      throw new Error(`Error al guardar la consulta: ${error.message}`)
    }

    console.log('Contact saved successfully:', contact.id)
    return contact
  } catch (error) {
    console.error('Error saving contact:', error)
    throw new Error('Error al guardar la consulta en la base de datos')
  }
}

// Función para buscar contactos duplicados
export async function findDuplicateContacts(email: string): Promise<Contact[]> {
  try {
    const { data: contacts, error } = await supabase
      .from('contacts')
      .select('*')
      .eq('email', email)
      .order('created_at', { ascending: false })
      .limit(5)

    if (error) {
      console.error('Error finding duplicate contacts:', error)
      return [] // Retornar array vacío en caso de error
    }

    return contacts || []
  } catch (error) {
    console.error('Error finding duplicate contacts:', error)
    return []
  }
}

// Función para obtener todos los contactos (solo para admin)
export async function getAllContacts(limit = 50, offset = 0): Promise<{
  contacts: Contact[]
  total: number
}> {
  try {
    // Obtener el total de contactos
    const { count, error: countError } = await supabaseAdmin
      .from('contacts')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      throw new Error(`Error counting contacts: ${countError.message}`)
    }

    // Obtener los contactos paginados
    const { data: contacts, error } = await supabaseAdmin
      .from('contacts')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      throw new Error(`Error fetching contacts: ${error.message}`)
    }

    return {
      contacts: contacts || [],
      total: count || 0
    }
  } catch (error) {
    console.error('Error getting all contacts:', error)
    throw error
  }
}

// Función para obtener un contacto por ID
export async function getContactById(id: number): Promise<Contact | null> {
  try {
    const { data: contact, error } = await supabaseAdmin
      .from('contacts')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return null // No encontrado
      }
      throw new Error(`Error fetching contact: ${error.message}`)
    }

    return contact
  } catch (error) {
    console.error('Error getting contact by ID:', error)
    throw error
  }
}

// Función para actualizar un contacto
export async function updateContact(id: number, updates: Partial<ContactFormData>): Promise<Contact> {
  try {
    const { data: contact, error } = await supabaseAdmin
      .from('contacts')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Error updating contact: ${error.message}`)
    }

    return contact
  } catch (error) {
    console.error('Error updating contact:', error)
    throw error
  }
}

// Función para eliminar un contacto
export async function deleteContact(id: number): Promise<void> {
  try {
    const { error } = await supabaseAdmin
      .from('contacts')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Error deleting contact: ${error.message}`)
    }
  } catch (error) {
    console.error('Error deleting contact:', error)
    throw error
  }
}

// Función para obtener estadísticas de contactos
export async function getContactStats(): Promise<{
  total: number
  thisMonth: number
  thisWeek: number
  byService: Record<string, number>
}> {
  try {
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()))

    // Total de contactos
    const { count: total } = await supabaseAdmin
      .from('contacts')
      .select('*', { count: 'exact', head: true })

    // Contactos de este mes
    const { count: thisMonth } = await supabaseAdmin
      .from('contacts')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfMonth.toISOString())

    // Contactos de esta semana
    const { count: thisWeek } = await supabaseAdmin
      .from('contacts')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfWeek.toISOString())

    // Contactos por servicio
    const { data: serviceData } = await supabaseAdmin
      .from('contacts')
      .select('service')

    const byService: Record<string, number> = {}
    serviceData?.forEach(contact => {
      byService[contact.service] = (byService[contact.service] || 0) + 1
    })

    return {
      total: total || 0,
      thisMonth: thisMonth || 0,
      thisWeek: thisWeek || 0,
      byService
    }
  } catch (error) {
    console.error('Error getting contact stats:', error)
    throw error
  }
}

// Función para verificar la conexión a Supabase
export async function testSupabaseConnection(): Promise<{ success: boolean; message: string }> {
  try {
    const { data, error } = await supabase
      .from('contacts')
      .select('count')
      .limit(1)

    if (error) {
      return {
        success: false,
        message: `Error de conexión: ${error.message}`
      }
    }

    return {
      success: true,
      message: 'Conexión a Supabase exitosa'
    }
  } catch (error) {
    return {
      success: false,
      message: `Error de conexión: ${error instanceof Error ? error.message : 'Error desconocido'}`
    }
  }
}
