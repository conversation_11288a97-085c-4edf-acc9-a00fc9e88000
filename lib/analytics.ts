// Analytics and monitoring utilities

export interface AnalyticsEvent {
  name: string
  properties?: Record<string, any>
  timestamp?: Date
}

export class Analytics {
  private static instance: Analytics
  private events: AnalyticsEvent[] = []

  static getInstance(): Analytics {
    if (!Analytics.instance) {
      Analytics.instance = new Analytics()
    }
    return Analytics.instance
  }

  // Track custom events
  track(name: string, properties?: Record<string, any>) {
    const event: AnalyticsEvent = {
      name,
      properties,
      timestamp: new Date(),
    }

    this.events.push(event)

    // In production, send to analytics service
    if (process.env.NODE_ENV === 'production') {
      this.sendToAnalytics(event)
    } else {
      console.log('Analytics Event:', event)
    }
  }

  // Track page views
  trackPageView(page: string, properties?: Record<string, any>) {
    this.track('page_view', {
      page,
      ...properties,
    })
  }

  // Track form submissions
  trackFormSubmission(formName: string, success: boolean, properties?: Record<string, any>) {
    this.track('form_submission', {
      form_name: formName,
      success,
      ...properties,
    })
  }

  // Track errors
  trackError(error: Error, context?: Record<string, any>) {
    this.track('error', {
      error_message: error.message,
      error_stack: error.stack,
      ...context,
    })
  }

  // Track performance metrics
  trackPerformance(metric: string, value: number, unit: string = 'ms') {
    this.track('performance', {
      metric,
      value,
      unit,
    })
  }

  private async sendToAnalytics(event: AnalyticsEvent) {
    try {
      // Here you would send to your analytics service
      // For example: Google Analytics, Mixpanel, etc.
      
      // Example with a generic analytics endpoint
      await fetch('/api/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      })
    } catch (error) {
      console.error('Failed to send analytics event:', error)
    }
  }

  // Get events for debugging
  getEvents(): AnalyticsEvent[] {
    return [...this.events]
  }

  // Clear events
  clearEvents() {
    this.events = []
  }
}

// Convenience functions
export const analytics = Analytics.getInstance()

export const trackEvent = (name: string, properties?: Record<string, any>) => {
  analytics.track(name, properties)
}

export const trackPageView = (page: string, properties?: Record<string, any>) => {
  analytics.trackPageView(page, properties)
}

export const trackFormSubmission = (formName: string, success: boolean, properties?: Record<string, any>) => {
  analytics.trackFormSubmission(formName, success, properties)
}

export const trackError = (error: Error, context?: Record<string, any>) => {
  analytics.trackError(error, context)
}

export const trackPerformance = (metric: string, value: number, unit?: string) => {
  analytics.trackPerformance(metric, value, unit)
}
