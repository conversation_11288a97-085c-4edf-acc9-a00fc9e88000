"use server"

import { z } from "zod"
import { sendContactEmails } from "./email-service"
import { saveContact, findDuplicateContacts } from "./supabase-database"

// Función que usa MCP de Supabase directamente
async function insertContactWithMCP(contactData: any) {
  console.log("💾 Insertando contacto con MCP de Supabase...")

  try {
    // Preparar query SQL para inserción
    const insertQuery = `
      INSERT INTO contacts (
        name, email, phone, company, service, message,
        budget, timeline, priority, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), NOW()
      ) RETURNING id, created_at, updated_at;
    `

    const values = [
      contactData.name,
      contactData.email,
      contactData.phone,
      contactData.company,
      contactData.service,
      contactData.message,
      contactData.budget,
      contactData.timeline,
      contactData.priority || 'normal'
    ]

    console.log("📝 Ejecutando query SQL:", insertQuery)
    console.log("📊 Valores:", values)

    // Simular inserción por ahora - TODO: Implementar MCP real
    const simulatedId = Date.now()
    const now = new Date().toISOString()

    console.log("✅ Inserción simulada exitosa, ID:", simulatedId)

    return {
      id: simulatedId,
      ...contactData,
      created_at: now,
      updated_at: now
    }

  } catch (error) {
    console.error("Error en insertContactWithMCP:", error)
    throw error
  }
}

// Schema de validación para el formulario de contacto - España
const contactSchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  email: z.string().email("Introduce un email válido"),
  phone: z
    .string()
    .optional()
    .transform((v) => v ? v.replace(/\D/g, "") : "") // Opcional
    .refine((v) => !v || v.length >= 9, {
      message: "El teléfono debe tener al menos 9 dígitos",
    }),
  company: z.string().optional(),
  service: z.string().min(1, "Selecciona un servicio"),
  message: z
    .string()
    .trim()
    .min(10, "El mensaje debe tener al menos 10 caracteres"),
  budget: z.string().optional(),
  timeline: z.string().optional(),
})

export type ContactFormData = z.infer<typeof contactSchema>

export interface ContactFormState {
  success?: boolean
  message?: string
  errors?: Record<string, string[]>
  emailSent?: boolean
  contactId?: number
}

// Función que usa MCP de Supabase directamente
export async function submitContactFormMCP(data: ContactFormData): Promise<ContactFormState> {
  console.log("=== FUNCIÓN CON MCP DE SUPABASE ===")
  console.log("Datos recibidos:", data)

  try {
    // Validación básica
    if (!data || !data.name || !data.email || !data.service) {
      return {
        success: false,
        message: "Faltan campos requeridos"
      }
    }

    // Preparar mensaje mínimo si está vacío
    const message = data.message?.trim() || `Consulta sobre ${data.service}`

    // Preparar datos para inserción directa
    const contactData = {
      name: data.name.trim(),
      email: data.email.trim(),
      phone: data.phone?.trim() || null,
      company: data.company?.trim() || null,
      service: data.service,
      message: message,
      budget: data.budget || null,
      timeline: data.timeline || null,
      priority: 'normal'
    }

    console.log("Datos preparados para MCP:", contactData)

    // Insertar directamente usando MCP de Supabase
    console.log("💾 Insertando con MCP de Supabase...")

    try {
      // Usar MCP para insertar en la base de datos
      const insertResult = await insertContactWithMCP(contactData)
      console.log("✅ Inserción exitosa con MCP:", insertResult)

      return {
        success: true,
        message: `¡Perfecto, ${data.name}! 🎉

Tu consulta sobre "${data.service}" ha sido registrada exitosamente usando MCP de Supabase.

⏰ Nuestro equipo te contactará en las próximas 24 horas
🚀 Mientras tanto, puedes <NAME_EMAIL>

📋 Número de consulta: #${insertResult.id}

¡Gracias por confiar en Karedesk España!`,
        contactId: insertResult.id
      }
    } catch (insertError) {
      console.error("Error insertando con MCP:", insertError)
      throw insertError
    }

  } catch (error) {
    console.error("Error en submitContactFormMCP:", error)
    return {
      success: false,
      message: "Error procesando la consulta con MCP"
    }
  }
}

// Función de prueba ultra simple sin dependencias
export async function submitContactFormSimple(data: ContactFormData): Promise<ContactFormState> {
  console.log("=== FUNCIÓN SIMPLE DE PRUEBA ===")
  console.log("Datos recibidos:", data)

  // Envolver todo en try-catch extremo
  try {
    console.log("Paso 1: Verificando datos")

    if (!data) {
      console.log("Error: No hay datos")
      return { success: false, message: "No hay datos" }
    }

    console.log("Paso 2: Verificando nombre")
    if (!data.name) {
      console.log("Error: No hay nombre")
      return { success: false, message: "No hay nombre" }
    }

    console.log("Paso 3: Verificando email")
    if (!data.email) {
      console.log("Error: No hay email")
      return { success: false, message: "No hay email" }
    }

    console.log("Paso 4: Todo OK, retornando éxito")
    return {
      success: true,
      message: "Prueba exitosa - función simple funcionando"
    }

  } catch (error) {
    console.error("Error capturado en función simple:", error)
    console.error("Tipo de error:", typeof error)
    console.error("Error es undefined?:", error === undefined)
    console.error("Error es null?:", error === null)

    return {
      success: false,
      message: "Error en función simple"
    }
  }
}

// Función de prueba básica (debe ser async para Server Actions)
export async function submitContactFormSync(data: ContactFormData): Promise<ContactFormState> {
  console.log("=== FUNCIÓN BÁSICA DE PRUEBA ===")
  console.log("Datos:", data)

  try {
    if (!data || !data.name || !data.email) {
      return { success: false, message: "Faltan datos" }
    }

    return { success: true, message: "Función básica exitosa" }
  } catch (error) {
    console.error("Error en función básica:", error)
    return { success: false, message: "Error en función básica" }
  }
}

// Función para envío directo desde componentes (sin FormData)
export async function submitContactForm(data: ContactFormData): Promise<ContactFormState> {
  console.log("=== INICIO submitContactForm ===")
  console.log("Datos recibidos:", JSON.stringify(data, null, 2))

  // Envolver absolutamente todo en try-catch
  try {
    console.log("Paso 1: Verificando existencia de datos")

    // Validar que los datos existen
    if (!data) {
      console.error("ERROR: No se recibieron datos del formulario")
      return {
        success: false,
        message: "No se recibieron datos del formulario"
      }
    }

    console.log("Paso 2: Verificando campos requeridos")

    // Validar campos básicos requeridos
    if (!data.name || !data.email || !data.service) {
      console.error("ERROR: Faltan campos requeridos", { name: data.name, email: data.email, service: data.service })
      return {
        success: false,
        message: "Faltan campos requeridos: nombre, email y servicio"
      }
    }

    console.log("✅ Validación básica pasada")

    console.log("Paso 3: Intentando validación con schema")

    // Validar datos con schema - envolver en try-catch específico
    let validatedData
    try {
      console.log("Llamando contactSchema.parse...")
      validatedData = contactSchema.parse(data)
      console.log("✅ Datos validados exitosamente:", validatedData)
    } catch (schemaError) {
      console.error("ERROR en validación de schema:", schemaError)
      console.error("Tipo de schemaError:", typeof schemaError)
      console.error("schemaError es undefined?:", schemaError === undefined)

      // Re-lanzar el error para que sea capturado por el catch principal
      throw schemaError
    }

    // Verificar si ya existe una consulta reciente del mismo email
    console.log("🔍 Verificando duplicados para email:", validatedData.email)
    let duplicates = []
    try {
      duplicates = await findDuplicateContacts(validatedData.email)
      console.log("✅ Duplicados encontrados:", duplicates.length)
    } catch (duplicateError) {
      console.error("ERROR verificando duplicados:", duplicateError)
      // Continuar sin verificar duplicados
    }

    const recentDuplicate = duplicates.find(
      (contact) => new Date(contact.created_at) > new Date(Date.now() - 24 * 60 * 60 * 1000), // últimas 24 horas
    )

    if (recentDuplicate) {
      console.log(`⚠️ Contacto duplicado detectado para ${validatedData.email}:`, recentDuplicate.id)
      return {
        success: true,
        message: `Hola ${validatedData.name}! 👋

Ya hemos recibido una consulta tuya recientemente. Nuestro equipo está revisando tu solicitud y te contactaremos pronto.

Si necesitas agregar información adicional o es urgente, puedes llamarnos directamente al +1 (555) 123-4567.

¡Gracias por tu paciencia!`,
        contactId: recentDuplicate.id,
      }
    }

    // Intentar enviar emails (no bloquear si falla)
    console.log("📧 Iniciando envío de emails...")
    let emailResult = { success: false, clientEmailId: null, internalEmailId: null }
    try {
      emailResult = await sendContactEmails(validatedData)
      console.log("✅ Emails enviados exitosamente:", emailResult)
    } catch (emailError) {
      console.error("⚠️ Error enviando emails (continuando con guardado):", emailError)
      // No lanzar error, continuar con el guardado
    }

    // Guardar en base de datos
    console.log("💾 Guardando contacto en base de datos...")
    let savedContact
    try {
      savedContact = await saveContact({
        ...validatedData,
        client_email_id: emailResult.clientEmailId,
        internal_email_id: emailResult.internalEmailId,
      })
      console.log("✅ Contacto guardado exitosamente:", savedContact)
    } catch (saveError) {
      console.error("ERROR guardando contacto:", saveError)
      throw new Error(`Error al guardar la consulta: ${saveError instanceof Error ? saveError.message : 'Error desconocido'}`)
    }

    // Log para debugging
    console.log("Consulta guardada exitosamente:", {
      id: savedContact.id,
      email: savedContact.email,
      service: savedContact.service,
    })

    const successMessage = emailResult.success
      ? `¡Perfecto, ${validatedData.name}! 🎉

Tu consulta sobre "${validatedData.service}" ha sido registrada exitosamente.

📧 Te hemos enviado un email de confirmación a ${validatedData.email}
⏰ Nuestro equipo te contactará en las próximas 24 horas
🚀 Mientras tanto, puedes <NAME_EMAIL>

📋 Número de consulta: #${savedContact.id}

¡Gracias por confiar en Karedesk España!`
      : `¡Perfecto, ${validatedData.name}! 🎉

Tu consulta sobre "${validatedData.service}" ha sido registrada exitosamente.

⏰ Nuestro equipo te contactará en las próximas 24 horas
📧 Te enviaremos la confirmación por email en breve
🚀 Mientras tanto, puedes <NAME_EMAIL>

📋 Número de consulta: #${savedContact.id}

¡Gracias por confiar en Karedesk España!`

    return {
      success: true,
      emailSent: emailResult.success,
      contactId: savedContact.id,
      message: successMessage,
    }
  } catch (error) {
    console.error("=== ERROR EN submitContactForm ===")
    console.error("Error completo:", error)
    console.error("Tipo de error:", typeof error)
    console.error("Es Error?:", error instanceof Error)
    console.error("Es ZodError?:", error instanceof z.ZodError)

    if (error instanceof Error) {
      console.error("Error message:", error.message)
      console.error("Error stack:", error.stack)
    } else {
      console.error("Error no es instancia de Error:", error)
    }

    // Manejo específico de errores Zod
    if (error instanceof z.ZodError) {
      console.error("❌ Error de validación Zod:", error.flatten())
      return {
        success: false,
        message: "Por favor corrige los errores en el formulario",
        errors: error.flatten().fieldErrors,
      }
    }

    // Error específico de email
    if (error instanceof Error && error.message.includes("email")) {
      console.error("❌ Error específico de email:", error.message)
      return {
        success: false,
        message: "Tu consulta fue recibida pero hubo un problema enviando el email de confirmación. Te contactaremos pronto.",
      }
    }

    // Error de base de datos
    if (error instanceof Error && (error.message.includes("base de datos") || error.message.includes("guardar"))) {
      console.error("❌ Error de base de datos:", error.message)
      return {
        success: false,
        message: "Hubo un problema técnico procesando tu consulta. Por favor intenta nuevamente o contáctanos <NAME_EMAIL>.",
      }
    }

    // Error genérico
    const errorMessage = error instanceof Error ? error.message : (error ? String(error) : 'Error desconocido')
    console.error("❌ Error genérico:", errorMessage)

    return {
      success: false,
      message: "Hubo un error procesando tu consulta. Por favor intenta nuevamente o contáctanos <NAME_EMAIL>.",
      error: errorMessage
    }
  } finally {
    console.log("=== FIN submitContactForm ===")
  }
}

// Función para formularios con FormData (para compatibilidad con useFormState)
export async function submitContactFormWithFormData(prevState: ContactFormState, formData: FormData): Promise<ContactFormState> {
  try {
    // Extraer datos del formulario
    const data = {
      name: formData.get("name") as string,
      email: formData.get("email") as string,
      phone: formData.get("phone") as string,
      company: formData.get("company") as string,
      service: formData.get("service") as string,
      message: formData.get("message") as string,
      budget: formData.get("budget") as string,
      timeline: formData.get("timeline") as string,
    }

    // Usar la función principal
    return await submitContactForm(data)
  } catch (error) {
    console.error("Error en submitContactFormWithFormData:", error)
    return {
      success: false,
      message:
        "Hubo un error procesando tu consulta. Por favor intenta nuevamente o contáctanos <NAME_EMAIL>.",
    }
  }
}

// Función para obtener estadísticas de contactos (opcional)
export async function getContactStats() {
  // Aquí podrías implementar estadísticas desde tu base de datos
  return {
    totalContacts: 0,
    contactsThisMonth: 0,
    responseRate: 0,
  }
}
