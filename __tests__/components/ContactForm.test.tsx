import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ContactForm from '@/components/contact-form'

// Mock the contact action
jest.mock('@/lib/contact-actions', () => ({
  submitContactForm: jest.fn(),
}))

describe('ContactForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders contact form with all fields', () => {
    render(<ContactForm />)
    
    expect(screen.getByLabelText(/nombre completo/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/teléfono/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/empresa/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/servicio/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/presupuesto/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/timeline/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/mensaje/i)).toBeInTheDocument()
  })

  it('shows validation errors for required fields', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)
    
    const submitButton = screen.getByRole('button', { name: /enviar consulta/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/el nombre es requerido/i)).toBeInTheDocument()
      expect(screen.getByText(/el email es requerido/i)).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    const user = userEvent.setup()
    render(<ContactForm />)
    
    const emailInput = screen.getByLabelText(/email/i)
    await user.type(emailInput, 'invalid-email')
    
    const submitButton = screen.getByRole('button', { name: /enviar consulta/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByText(/email inválido/i)).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const { submitContactForm } = require('@/lib/contact-actions')
    submitContactForm.mockResolvedValue({ success: true })
    
    const user = userEvent.setup()
    render(<ContactForm />)
    
    // Fill form with valid data
    await user.type(screen.getByLabelText(/nombre completo/i), 'Juan Pérez')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/teléfono/i), '*********')
    await user.type(screen.getByLabelText(/empresa/i), 'Mi Empresa')
    await user.selectOptions(screen.getByLabelText(/servicio/i), 'Consultoría IA')
    await user.selectOptions(screen.getByLabelText(/presupuesto/i), '5000-10000')
    await user.selectOptions(screen.getByLabelText(/timeline/i), '1-2-meses')
    await user.type(screen.getByLabelText(/mensaje/i), 'Mensaje de prueba')
    
    const submitButton = screen.getByRole('button', { name: /enviar consulta/i })
    await user.click(submitButton)
    
    await waitFor(() => {
      expect(submitContactForm).toHaveBeenCalledWith(expect.objectContaining({
        name: 'Juan Pérez',
        email: '<EMAIL>',
        phone: '*********',
        company: 'Mi Empresa',
        service: 'Consultoría IA',
        budget: '5000-10000',
        timeline: '1-2-meses',
        message: 'Mensaje de prueba',
      }))
    })
  })
})
