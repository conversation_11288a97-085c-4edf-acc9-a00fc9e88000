import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import ContactForm from '@/components/contact-form'

// Mock the contact action
jest.mock('@/lib/contact-actions', () => ({
  submitContactFormWithFormData: jest.fn(),
}))

describe('ContactForm', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders contact form with required fields', () => {
    render(<ContactForm service="Consultoría IA" />)

    expect(screen.getByLabelText(/nombre completo/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/teléfono/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/empresa/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/cuéntanos sobre tu proyecto/i)).toBeInTheDocument()
  })

  it('shows validation errors for required fields', async () => {
    const user = userEvent.setup()
    render(<ContactForm service="Consultoría IA" />)

    const submitButton = screen.getByRole('button', { name: /enviar consulta/i })
    await user.click(submitButton)

    // El formulario debería mostrar errores de validación
    await waitFor(() => {
      expect(screen.getByText(/completa todos los campos requeridos/i)).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    const user = userEvent.setup()
    render(<ContactForm service="Consultoría IA" />)

    const emailInput = screen.getByLabelText(/email/i)
    await user.type(emailInput, 'invalid-email')
    await user.tab() // Trigger blur event

    await waitFor(() => {
      expect(screen.getByText(/introduce un email válido/i)).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const { submitContactFormWithFormData } = require('@/lib/contact-actions')
    submitContactFormWithFormData.mockResolvedValue({ success: true })

    const user = userEvent.setup()
    render(<ContactForm service="Consultoría IA" />)

    // Fill form with valid data
    await user.type(screen.getByLabelText(/nombre completo/i), 'Juan Pérez')
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>')
    await user.type(screen.getByLabelText(/teléfono/i), '123456789')
    await user.type(screen.getByLabelText(/empresa/i), 'Mi Empresa')
    await user.type(screen.getByLabelText(/cuéntanos sobre tu proyecto/i), 'Mensaje de prueba para el proyecto')

    const submitButton = screen.getByRole('button', { name: /enviar consulta/i })
    await user.click(submitButton)

    await waitFor(() => {
      expect(submitContactFormWithFormData).toHaveBeenCalled()
    })
  })
})
